# 历史阅读列表接口文档

## 接口概述

`/api/v1/user/history/list` 接口用于获取用户的历史阅读记录。

## 接口详情

### 基本信息
- **接口路径**: `/api/v1/user/history/list`
- **请求方法**: `GET`
- **需要认证**: 是（需要 Bearer Token）

### 请求参数

#### Query 参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNo | number | 否 | 1 | 页码，从1开始 |
| pageSize | number | 否 | 20 | 每页大小，范围1-100 |

### 请求示例

```bash
GET /api/v1/user/history/list?pageNo=1&pageSize=10
Authorization: Bearer your-auth-token
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 200,
  "message": "获取历史阅读列表成功",
  "data": {
    "total": 25,
    "pageNo": 1,
    "pageSize": 10,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false,
    "histories": [
      {
        "id": "history_id_1",
        "bookId": "book_id_1",
        "progress": 50,
        "lastViewedTime": "2024-01-15T10:30:00Z",
        "createTime": "2024-01-10T09:00:00Z",
        "book": {
          "id": "book_id_1",
          "title": "示例书籍",
          "cover": "https://example.com/cover.jpg",
          "url": "https://example.com/book.pdf",
          "level": "intermediate",
          "categoryId": "category_id_1",
          "readCount": 100,
          "favoriteCount": 50,
          "createTime": "2024-01-01T00:00:00Z",
          "updateTime": "2024-01-15T10:30:00Z",
          "isFree": true
        }
      }
    ]
  }
}
```

#### 错误响应

**未登录 (401)**
```json
{
  "code": 401,
  "message": "用户未登录",
  "data": null
}
```

**参数错误 (400)**
```json
{
  "code": 400,
  "message": "页码必须大于等于1",
  "data": null
}
```

**服务器错误 (500)**
```json
{
  "code": 500,
  "message": "获取历史阅读列表失败: 具体错误信息",
  "data": null
}
```

### 响应字段说明

#### data 字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | number | 总记录数 |
| pageNo | number | 当前页码 |
| pageSize | number | 每页大小 |
| totalPages | number | 总页数 |
| hasNext | boolean | 是否有下一页 |
| hasPrev | boolean | 是否有上一页 |
| histories | array | 历史记录列表 |

#### histories 数组中的对象
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 历史记录ID |
| bookId | string | 书籍ID |
| progress | number | 阅读进度 |
| lastViewedTime | string | 最后查看时间 |
| createTime | string | 创建时间 |
| book | object | 书籍详细信息 |

#### book 对象
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 书籍ID |
| title | string | 书籍标题 |
| cover | string | 封面图片URL |
| url | string | 书籍文件URL |
| level | string | 难度等级 |
| categoryId | string | 分类ID |
| readCount | number | 阅读次数 |
| favoriteCount | number | 收藏次数 |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |
| isFree | boolean | 是否免费 |

## 使用示例

### JavaScript/TypeScript
```javascript
const getHistoryList = async (pageNo = 1, pageSize = 20) => {
  try {
    const response = await fetch(
      `/api/v1/user/history/list?pageNo=${pageNo}&pageSize=${pageSize}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('历史阅读列表:', result.data);
      return result.data;
    } else {
      console.error('获取历史阅读列表失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
};
```

### cURL
```bash
curl -X GET \
  "http://localhost:8787/api/v1/user/history/list?pageNo=1&pageSize=10" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json"
```

## 注意事项

1. 该接口需要用户登录，必须在请求头中携带有效的 Bearer Token
2. 历史记录按最后查看时间倒序排列（最近查看的在前面）
3. 分页参数 pageNo 从 1 开始，pageSize 最大值为 100
4. 返回的书籍信息包含完整的书籍详情，方便前端直接使用 