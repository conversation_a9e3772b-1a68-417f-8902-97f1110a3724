# 书籍详情接口文档

## 接口概述

`/api/v1/book/detail/:id` 接口用于获取指定书籍的详细信息，包括阅读进度和测验状态。

## 接口详情

### 基本信息
- **接口路径**: `/api/v1/book/detail/:id`
- **请求方法**: `GET`
- **需要认证**: 否（但认证用户会返回个人阅读进度）
- **功能**: 获取书籍详情信息

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 书籍ID |

### 请求示例

```bash
GET /api/v1/book/detail/book-id-123
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 200,
  "message": "获取书籍详情成功",
  "data": {
    "bookId": "book-id-123",
    "bookName": "JavaScript高级程序设计",
    "bookUrl": "https://example.com/book.pdf",
    "progress": 25,
    "quizStatus": 0
  }
}
```

#### 错误响应

**书籍不存在 (404)**
```json
{
  "code": 404,
  "message": "书籍不存在",
  "data": null
}
```

**参数错误 (400)**
```json
{
  "code": 400,
  "message": "书籍ID不能为空",
  "data": null
}
```

**服务器错误 (500)**
```json
{
  "code": 500,
  "message": "获取书籍详情失败: 具体错误信息",
  "data": null
}
```

### 响应字段说明

#### data 字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| bookId | string | 书籍ID |
| bookName | string | 书籍名称 |
| bookUrl | string | 书籍文件URL |
| progress | number | 阅读进度（页码），未登录或无记录时返回0 |
| quizStatus | number | 测验状态，暂时固定为0 |

## 使用示例

### JavaScript/TypeScript
```javascript
const getBookDetail = async (bookId) => {
  try {
    const response = await fetch(
      `/api/v1/book/detail/${bookId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // 可选：添加认证头以获取个人阅读进度
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('书籍详情:', result.data);
      return result.data;
    } else {
      console.error('获取书籍详情失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
};

// 使用示例
getBookDetail('book-id-123')
  .then(data => {
    console.log('书籍名称:', data.bookName);
    console.log('阅读进度:', data.progress);
    console.log('测验状态:', data.quizStatus);
  })
  .catch(error => {
    console.error('获取失败:', error);
  });
```

### cURL
```bash
# 基本请求
curl -X GET \
  "http://localhost:8787/api/v1/book/detail/book-id-123" \
  -H "Content-Type: application/json"

# 带认证的请求（获取个人阅读进度）
curl -X GET \
  "http://localhost:8787/api/v1/book/detail/book-id-123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-auth-token"
```

## 功能特性

### 阅读进度获取
- **已登录用户**: 从 `history` 表获取用户的阅读进度
- **未登录用户**: 返回默认进度 0
- **无阅读记录**: 返回默认进度 0

### 测验状态
- 目前固定返回 0
- 为后续测验功能预留字段

### 错误处理
- 书籍不存在时返回 404 错误
- 参数错误时返回 400 错误
- 服务器错误时返回 500 错误

## 注意事项

1. **认证可选**: 该接口不需要认证，但认证用户会返回个人阅读进度
2. **进度准确性**: 阅读进度从 `history` 表实时获取，确保数据准确性
3. **向后兼容**: 接口设计考虑了后续功能扩展
4. **性能优化**: 使用单次数据库查询获取所需信息

## 与其他接口的区别

- **`/api/v1/book/:id/url`**: 只返回书籍URL
- **`/api/v1/book/detail/:id`**: 返回完整的书籍详情信息，包括进度和状态 