/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "kr-cfw",
	"main": "src/index.ts",
	"compatibility_date": "2025-06-17",
	/**
	 * 本地开发 配置
	 */
	"dev": {
		"ip": "0.0.0.0",
		"port": 8787,
		"local_protocol": "http"
	},
	"observability": {
		"enabled": true
	},
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },
	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	"vars": {
		"MY_VARIABLE": "production_value",
		"USER_NAME": "Jack",
		"USER_AGE": 18,
		"R2_PUBLIC_DOMAIN": "https://res.youggst.com",
		"APPLE_ISSUER": "https://appleid.apple.com",
		"APPLE_CLIENT_ID": "com.xiaochuang.picturebook",
		"DEFAULT_AVATAR": "https://res.youggst.com/sw_default_avatar.webp",
		"R2_BUCKET_NAME": "kr-public-bucket",
		"ENDPOINT": "https://487342cd13fc2d900f4580d735df40ed.r2.cloudflarestorage.com", // S3
		"R2_ACCESS_KEY_ID": "0ba888bfd82b5a2a5e380dd37341919e", // S3 访问密钥 ID
		"R2_SECRET_ACCESS_KEY": "c23127bed7560d0d34a47ed3717809eca2e05977a22b774876052750b592ac70", // S3 秘密访问密钥
	},
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "kr-db",
			"database_id": "ddddea49-4215-4005-bf63-1d46481cfc5f",
			"migrations_dir": "drizzle/migrations" // 迁移文件目录
		}
	],
    "r2_buckets": [
      {
        "binding": "KR_PUBLIC_BUCKET",
        "bucket_name": "kr-public-bucket"
      }
    ],
 "kv_namespaces": [
  {
   "binding": "KR_CONFIG",
   "id": "464003393fe042e3a69da98792374f0a",
   "preview_id": "986b29cf3d6d42aab4aa6fbccf47686d"
  }
 ],
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */
	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },
	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}