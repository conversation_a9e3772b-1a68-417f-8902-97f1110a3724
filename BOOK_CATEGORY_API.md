# 分类书籍列表接口文档

## 接口概述

`/api/v1/book/category/:categoryId` 接口用于获取指定分类下的所有书籍数据。

## 接口详情

### 基本信息
- **接口路径**: `/api/v1/book/category/:categoryId`
- **请求方法**: `GET`
- **需要认证**: 否（公开接口）
- **功能**: 获取指定分类下的书籍列表

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | string | 是 | 分类ID |

### Query 参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNo | number | 否 | 1 | 页码，从1开始 |
| pageSize | number | 否 | 20 | 每页大小，范围1-100 |

### 请求示例

```bash
GET /api/v1/book/category/tech-category-id?pageNo=1&pageSize=10
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 200,
  "message": "获取分类书籍列表成功",
  "data": {
    "total": 25,
    "pageNo": 1,
    "pageSize": 10,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false,
    "category": {
      "id": "tech-category-id",
      "categoryName": "技术书籍",
      "cover": "https://example.com/category-cover.jpg",
      "createTime": "2024-01-01T00:00:00Z",
      "updateTime": "2024-01-15T10:30:00Z"
    },
    "books": [
      {
        "id": "book_id_1",
        "title": "JavaScript高级程序设计",
        "cover": "https://example.com/book-cover.jpg",
        "url": "https://example.com/book.pdf",
        "level": 2,
        "categoryId": "tech-category-id",
        "readCount": 150,
        "favoriteCount": 75,
        "createTime": "2024-01-10T09:00:00Z",
        "updateTime": "2024-01-15T10:30:00Z",
        "isFree": 1
      }
    ]
  }
}
```

#### 错误响应

**分类不存在 (404)**
```json
{
  "code": 404,
  "message": "分类不存在",
  "data": null
}
```

**参数错误 (400)**
```json
{
  "code": 400,
  "message": "页码必须大于等于1",
  "data": null
}
```

**服务器错误 (500)**
```json
{
  "code": 500,
  "message": "获取分类书籍列表失败: 具体错误信息",
  "data": null
}
```

### 响应字段说明

#### data 字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | number | 总记录数 |
| pageNo | number | 当前页码 |
| pageSize | number | 每页大小 |
| totalPages | number | 总页数 |
| hasNext | boolean | 是否有下一页 |
| hasPrev | boolean | 是否有上一页 |
| category | object | 分类信息 |
| books | array | 书籍列表 |

#### category 对象
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 分类ID |
| categoryName | string | 分类名称 |
| cover | string | 分类封面图片URL |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |

#### books 数组中的对象
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 书籍ID |
| title | string | 书籍标题 |
| cover | string | 封面图片URL |
| url | string | 书籍文件URL |
| level | number | 难度等级 |
| categoryId | string | 分类ID |
| readCount | number | 阅读次数 |
| favoriteCount | number | 收藏次数 |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |
| isFree | number | 是否免费（1=免费，0=付费） |

## 使用示例

### JavaScript/TypeScript
```javascript
const getBooksByCategory = async (categoryId, pageNo = 1, pageSize = 20) => {
  try {
    const response = await fetch(
      `/api/v1/book/category/${categoryId}?pageNo=${pageNo}&pageSize=${pageSize}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('分类书籍列表:', result.data);
      return result.data;
    } else {
      console.error('获取分类书籍列表失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
};

// 使用示例
getBooksByCategory('tech-category-id', 1, 10)
  .then(data => {
    console.log('分类信息:', data.category);
    console.log('书籍列表:', data.books);
    console.log('分页信息:', {
      total: data.total,
      currentPage: data.pageNo,
      totalPages: data.totalPages
    });
  })
  .catch(error => {
    console.error('获取失败:', error);
  });
```

### cURL
```bash
curl -X GET \
  "http://localhost:8787/api/v1/book/category/tech-category-id?pageNo=1&pageSize=10" \
  -H "Content-Type: application/json"
```

## 注意事项

1. 该接口是公开接口，不需要用户登录
2. 书籍按创建时间排序（最新创建的在前）
3. 分页参数 pageNo 从 1 开始，pageSize 最大值为 100
4. 如果分类不存在，会返回 404 错误
5. 返回的数据包含分类信息和该分类下的所有书籍
6. 每本书都包含完整的书籍信息，方便前端直接使用

## 与其他接口的区别

- **`/api/v1/book/list`**: 获取所有书籍（后台管理用）
- **`/api/v1/book/category/:categoryId`**: 获取指定分类下的书籍（前端展示用） 