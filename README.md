## wrangler

全局安装 wrangler

```shell
npm install -g wrangler
```
### 全局配置
#### wrangler.jsonc
> 该文件用声明环境变量，如：
```json
"vars": {
		"MY_VARIABLE": "production_value",
		"USER_NAME": "Jack",
		"USER_AGE": 18
	},
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "d1-tutorial",
			"database_id": "xxx-xxx-xxx",
			"migrations_dir": "drizzle/migrations" // 迁移文件目录
		}
	],
```
每当 `wrangler.jsonc` 中的内容改变，都需要执行如下命令 `wrangler types` 来更新 `worker-configuration.d.ts` 文件，然后我们在代码中获取环境变量的时候才不会报错
```ts
sayHello(ctx: Context<{ Bindings: Env }>) {
    const userName = ctx.env.USER_NAME;
    const age = ctx.env.USER_AGE;   
    return ctx.text(`Hello ${userName}, you are ${age} years old`);
}
```


## drizzle
用于将 ts 对象和数据库表映射的 ORM 库
文档：https://orm.drizzle.team/docs/kit-overview#how-to-migrate-to-0210

### ⚠️ 重要提示

💥**生成的迁移文件一定不能删！**

⚡️**否则会数据库的本地迁移或者远程迁移会报错**

### 依赖
```shell
# 添加 dependencies
npm i drizzle-orm

# 添加 devDependencies
npm i -d drizzle-kit
```

### 配置

#### package.json

```json
"scripts": {
    # 项目部署
    "deploy": "wrangler deploy",
    // 启动调试
    "dev": "wrangler dev",
    // 启动调试
    "start": "wrangler dev",
    "test": "vitest",
    // 用于刷新全局配置的
    "cf-typegen": "wrangler types",
    // 用户已生成迁移文件  ts --> sql 默认会随机生成迁移文件名
    "db:generate": "drizzle-kit generate",
    // 生成带自定义名称的迁移文件： npm run db:generate:named add_posts_author_column
    "db:generate:named": "drizzle-kit generate --name",
    // 用于使用 sql 创建本地 sqlite 数据库  sql --> sqlite
    "db:migrate": "wrangler d1 migrations apply d1-tutorial",
    // 用于将本地数据库的变化迁移到远程 d1 数据库
    "db:migrate:remote": "wrangler d1 migrations apply d1-tutorial --remote",
    // 启动本地数据库管理界面
    "db:studio": "drizzle-kit studio"
    // 创建数据库 npm run db:create blog-db
    "db:create": "wrangler d1 create",
    // 删除数据库 npm run db:delete blog-db
    "db:delete": "wrangler d1 delete",
    // 列出当前所有的数据库
    "db:list": "wrangler d1 list"
	},
```
### 使用步骤
#### 创建数据库
```shell
npm run db:create d1-tutorial
```
输出如下
```shell
> wrangler d1 create blog-db


 ⛅️ wrangler 4.20.0 (update available 4.20.1)
─────────────────────────────────────────────
✅ Successfully created DB 'd1-tutorial' in region WNAM
Created your new D1 database.

{
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "d1-tutorial",
      "database_id": "xxxx-xxxx-xxxx"
    }
  ]
}
```
#### 在 `wrangler.jsonc` 中添加 d1 配置


   ```json
   	"d1_databases": [
   		{
   			"binding": "DB",
   			"database_name": "d1-tutorial",
   			"database_id": "xxxxxxxx-xxx-xxxx-xxxx",
            // 迁移文件目录，写这一行时候可以使用 npm run db:migrate 来生成迁移文件
            "migrations_dir": "drizzle/migrations"
   		}
   	],
   ```

#### 创建 `drizzle.config.ts`

   ```ts
   import { defineConfig } from 'drizzle-kit';
   
   export default defineConfig({
     schema: './src/db/schema.ts', // 表结构定义文件
     out: './drizzle/migrations', // 迁移文件输出目录，db:generate 产物的路径
     dialect: 'sqlite', // 数据库类型
     // 数据库文件路径，配置了这一项可以使用 npm run db:studio 来可视化查看本地数据库
     dbCredentials: {
       // 该路径就是 npm run db:generate 生成sqlite文件路径
       url: 'file:.wrangler/state/v3/d1/miniflare-D1DatabaseObject/xxx.sqlite', 
     },
   }); 
   ```

#### `src/db/schema.ts` 定义

   > 使用TypeScript定义表结构

    ```ts
    import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
    import { sql } from 'drizzle-orm';
    
    export const posts = sqliteTable('posts', {
      id: integer('id').primaryKey({ autoIncrement: true }),
      title: text('title').notNull(),
      content: text('content').notNull(),
      createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
    });   
    ```

#### 使用命令生成迁移文件，就是将 schema.ts 生成对应的 .sql 文件

   ```shell
   npm run db:generate
   ```
   上述命令生成的文件位于 `./drizzle/migrations/xxxx.sql`

#### 接着使用如下命令使用上一步生成的 sql 文件同时在**本地**创建对应的表
使用 `npm run db:generate` 或者 `npm run db:generate:named 自定义名称`  来生成sql文件
本地的表可用于开发环境的测试
这个时候，远程 cf 中的 d1 是没有表的

#### 将表结构迁移到本地表
如果表结构有变化，需要将表结构同步到本地数据库，步骤如下：
1. `npm run db:generate` 生成迁移文件，如果有表未创建，该命令在创建迁移文件的同时也会将数据库表创建出来
2. `npm run db:migrate` 将迁移文件引用到本地数据库的表结构中

#### 将本地的表结构推送到远程 cf 的 d1 数据库中
项目开发完成，需将本地的表结构同步到远程d1数据库中，步骤如下：
1. `npm run db:generate` 先生成迁移文件
2. `npm run db:migrate:remote` 将迁移文件应用到远程数据库的表结构中

#### 本地数据库可视化
在开发阶段使用本地数据库，可以执行 `npm run db:studio` 来开启本地数据库的可视化页面，方便查看数据变化

### 数据库新增字段同步
- 生成迁移文件
- 删除 .wrangler/state 文件夹
- 迁移到本地
- 迁移到远程

## API 接口

### 用户相关接口

#### 历史阅读列表
- **路径**: `/api/v1/user/history/list`
- **方法**: `GET`
- **认证**: 需要 Bearer Token
- **功能**: 获取用户的历史阅读记录
- **参数**: 
  - `pageNo` (可选): 页码，默认 1
  - `pageSize` (可选): 每页大小，默认 20，最大 100
- **响应**: 包含分页信息和历史记录列表，每条记录包含书籍详细信息

详细文档请参考: [HISTORY_API.md](./HISTORY_API.md)

#### 分类书籍列表
- **路径**: `/api/v1/book/category/:categoryId`
- **方法**: `GET`
- **认证**: 不需要（公开接口）
- **功能**: 获取指定分类下的书籍列表
- **参数**: 
  - `categoryId` (路径参数): 分类ID
  - `pageNo` (可选): 页码，默认 1
  - `pageSize` (可选): 每页大小，默认 20，最大 100
- **响应**: 包含分类信息和该分类下的书籍列表，支持分页

详细文档请参考: [BOOK_CATEGORY_API.md](./BOOK_CATEGORY_API.md)

### Home 相关接口

#### Banner 书籍列表
- **路径**: `/api/v1/home/<USER>
- **方法**: `GET`
- **认证**: 需要 Bearer Token
- **功能**: 获取阅读次数最多的3本书籍
- **新增字段**: 包含评分信息和用户阅读进度

#### 推荐书籍列表
- **路径**: `/api/v1/home/<USER>
- **方法**: `GET`
- **认证**: 需要 Bearer Token
- **功能**: 获取收藏次数最多的3本书籍
- **新增字段**: 包含评分信息和用户阅读进度

#### 最近阅读记录
- **路径**: `/api/v1/home/<USER>
- **方法**: `GET`
- **认证**: 需要 Bearer Token
- **功能**: 获取用户最近3条阅读记录
- **新增字段**: 包含评分信息和阅读进度

#### 游客 Banner 书籍列表
- **路径**: `/api/v1/home/<USER>/banner`
- **方法**: `GET`
- **认证**: 不需要（公开接口）
- **功能**: 获取游客 Banner 书籍列表

#### 游客推荐书籍列表
- **路径**: `/api/v1/home/<USER>/recommend`
- **方法**: `GET`
- **认证**: 不需要（公开接口）
- **功能**: 获取游客推荐书籍列表

详细文档请参考: [HOME_RATING_PROGRESS_API.md](./HOME_RATING_PROGRESS_API.md)

### 书籍相关接口

#### 书籍详情
- **路径**: `/api/v1/book/detail/:id`
- **方法**: `GET`
- **认证**: 不需要（但认证用户会返回个人阅读进度）
- **功能**: 获取书籍详情信息
- **参数**: 
  - `id` (路径参数): 书籍ID
- **响应**: 包含书籍基本信息、阅读进度和测验状态

详细文档请参考: [BOOK_DETAIL_API.md](./BOOK_DETAIL_API.md)