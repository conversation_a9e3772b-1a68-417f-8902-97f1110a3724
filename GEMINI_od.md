# GEMINI.MD

# APP端模块介绍

> 说明：
1. 后端没有会员体系，会员与否完全依赖于从 AppleStore 获取的状态
2. 用户登录只是为了记录该用户的阅读历史和收藏记录
> 

## 首页

### Banner

- 用户是否会员
    - 是：展示评分数量最高的 3 本书籍
    - 否：展示固定的免费的 3 本书籍

### 最近阅读记录

- 用户是否登录
    - 是：展示当前用户最近阅读的 3 本书籍
    - 否：不展示该模块

### 推荐阅读

- 用户是否会员
    - 是：按照数据的阅读数量从高到低一次展示（分页加载）
    - 否：展示固定的免费的 3 本书籍

## 分类

- 展示所有的分类
- 点击分类可进入到该分类下的所有书籍列表
- 点击列表项
    - 该用户是否是会员
        - 是：可进入到书籍阅读页面
        - 否：弹出会员订阅页面
- 书籍阅读页面包含的功能如下：
    - 记录用户阅读历史
    - 为该书籍评分
    - 收藏/取消收藏 该书籍
    - 如果该书籍有 小测验，则可打开该书籍的小测验
    - 当用户选中 PDF 上的文本，可调后端AI接口来解释用户选择的单词或者句子

## 我的

- 用户信息
- 阅读历史
- 收藏记录
- 设置
    - 切换语言
    - 隐私记录
    - 用户协议
    - 退出登录

## 登录

- Apple 登录

## 支付

- Apple 支付

# 后端管理系统

> 该系统准备使用vue3开发，和大多数后台管理dashboard类似，左边是功能列表，右边展示列表项对应的页面
> 

## 左边分类

### 书籍

- 展示保存在 R2 中的所有书籍列表
- 上传书籍到 R2
- 对书籍的增删改查

### 分类

- 对分类的增删改查

# 后端项目框架

该项目是使用 cloudflare wrangler 框架构建的运行在 cloudflare workers 上的项目

## 后端项目简介

该项目是一个提供英语绘本阅读的的项目，所有的绘本文件都保存在 R2 对象存储中，项目中用到的数据库是 D1 数据库。

## 数据库表 + 功能介绍

### 用户表

```tsx
// 用户表
export const user = sqliteTable('user', {
  id: text('id', { length: 36 }).unique().primaryKey(),
  appleAuthId: text('apple_auth_id', { length: 255 }).unique(),
  name: text('name', { length: 100 }).notNull(),
  avatar: text('avatar', { length: 255 }),
  token: text('token', { length: 255 }).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});
```

- 用户信息的增删改查

### 书籍表

```tsx
// 书籍表
export const book = sqliteTable('book', {
  id: text('id').primaryKey(),
  title: text('title', { length: 255 }),
  cover: text('cover', { length: 255 }),
  fileName: text('file_name', { length: 255 }),
  level: integer('level'),
  categoryId: text('category_id'),
  readCount: integer('read_count').default(0),
  favoriteCount: integer('favorite_count').default(0),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  isFree: integer('is_free').default(0),
});
```

- 对书籍的增删改查

### 书籍分类表

```tsx
export const category = sqliteTable('category', {
  id: text('id').primaryKey(),
  categoryName: text('category_name', { length: 32 }),
  cover: text('cover', { length: 255 }),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});
```

功能：

- 用于新增书籍分类

### 收藏表

```tsx
export const favorite = sqliteTable('favorite', {
  id: text('id').primaryKey(),
  userId: text('user_id'),
  bookId: text('book_id'),
  progress: integer('progress').default(0),
  lastViewedTime: integer('last_viewed_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});
```

- 用于保存用户的收藏记录

### 历史表

```tsx
export const history = sqliteTable('history', {
  id: text('id').primaryKey(),
  userId: text('user_id'),
  bookId: text('book_id'),
  progress: integer('progress').default(0),
  lastViewedTime: integer('last_viewed_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});
```

- 用于保存用户的历史阅读记录

### 测试题表

```
export const quiz = sqliteTable('quiz', {
  id: text('id').primaryKey(),
  bookId: text('book_id'),
  content: text('content'),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});
```

- 用于保存书籍的测试提数据

### 评分表

```
export const rating = sqliteTable('rating', {
  id: text('id').primaryKey(),
  userId: text('user_id'),
  bookId: text('book_id'),
  score: integer('score'),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});
```

- 用于保存书籍的评分记录