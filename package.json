{"name": "kr-cfw", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types", "db:generate": "drizzle-kit generate", "db:generate:named": "drizzle-kit generate --name", "db:migrate": "wrangler d1 migrations apply kr-db", "db:migrate:remote": "wrangler d1 migrations apply kr-db --remote", "db:studio": "drizzle-kit studio", "db:create": "wrangler d1 create", "db:delete": "wrangler d1 delete", "db:list": "wrangler d1 list"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "@types/node": "^24.0.3", "drizzle-kit": "^0.31.1", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.20.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.888.0", "@aws-sdk/s3-request-presigner": "^3.888.0", "@google/genai": "^1.5.1", "axios": "^1.10.0", "drizzle-orm": "^0.44.2", "hono": "^4.8.0", "jose": "^6.1.0", "mime": "^4.0.7", "nanoid": "^5.1.5", "node-fetch": "^3.3.2"}, "type": "module"}