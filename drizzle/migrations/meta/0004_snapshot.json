{"version": "6", "dialect": "sqlite", "id": "90cce20c-8a47-4781-b673-8958be64e594", "prevId": "04d6252e-ef97-4c9c-b05c-b1d5e4272a36", "tables": {"book": {"name": "book", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover": {"name": "cover", "type": "text(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "url": {"name": "url", "type": "text(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "level": {"name": "level", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "read_count": {"name": "read_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "favorite_count": {"name": "favorite_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "is_free": {"name": "is_free", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "category": {"name": "category", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "category_name": {"name": "category_name", "type": "text(32)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover": {"name": "cover", "type": "text(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "favorite": {"name": "favorite", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "book_id": {"name": "book_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "last_viewed_time": {"name": "last_viewed_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "history": {"name": "history", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "book_id": {"name": "book_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "last_viewed_time": {"name": "last_viewed_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "quiz": {"name": "quiz", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "book_id": {"name": "book_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "quiz_record": {"name": "quiz_record", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "rating": {"name": "rating", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "book_id": {"name": "book_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_time": {"name": "create_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "update_time": {"name": "update_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text(36)", "primaryKey": true, "notNull": true, "autoincrement": false}, "apple_auth_id": {"name": "apple_auth_id", "type": "text(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "token": {"name": "token", "type": "text(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {"user_id_unique": {"name": "user_id_unique", "columns": ["id"], "isUnique": true}, "user_apple_auth_id_unique": {"name": "user_apple_auth_id_unique", "columns": ["apple_auth_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}