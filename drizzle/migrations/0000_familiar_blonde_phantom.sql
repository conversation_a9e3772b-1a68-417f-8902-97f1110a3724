CREATE TABLE `book` (
	`id` text PRIMARY KEY NOT NULL,
	`title` text(255),
	`cover` text(255),
	`file_name` text(255),
	`level` integer,
	`category_id` text,
	`read_count` integer DEFAULT 0,
	`favorite_count` integer DEFAULT 0,
	`create_time` integer DEFAULT (unixepoch()) NOT NULL,
	`update_time` integer DEFAULT (unixepoch()) NOT NULL,
	`is_free` integer DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE `category` (
	`id` text PRIMARY KEY NOT NULL,
	`category_name` text(32),
	`cover` text(255),
	`create_time` integer DEFAULT (unixepoch()) NOT NULL,
	`update_time` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `favorite` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text,
	`book_id` text,
	`progress` integer DEFAULT 0,
	`last_viewed_time` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `history` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text,
	`book_id` text,
	`progress` integer DEFAULT 0,
	`last_viewed_time` integer DEFAULT (unixepoch()) NOT NULL,
	`create_time` integer DEFAULT (unixepoch()) NOT NULL,
	`update_time` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `quiz` (
	`id` text PRIMARY KEY NOT NULL,
	`book_id` text,
	`content` text,
	`create_time` integer DEFAULT (unixepoch()) NOT NULL,
	`update_time` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `quiz_record` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text,
	`quiz_id` text,
	`create_time` integer DEFAULT (unixepoch()) NOT NULL,
	`update_time` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `rating` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text,
	`book_id` text,
	`score` integer,
	`create_time` integer DEFAULT (unixepoch()) NOT NULL,
	`update_time` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `user` (
	`id` text(36) PRIMARY KEY NOT NULL,
	`apple_auth_id` text(255),
	`name` text(100) NOT NULL,
	`avatar` text(255),
	`token` text(255) NOT NULL,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_id_unique` ON `user` (`id`);--> statement-breakpoint
CREATE UNIQUE INDEX `user_apple_auth_id_unique` ON `user` (`apple_auth_id`);