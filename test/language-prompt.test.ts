import { describe, it, expect } from 'vitest';
import { getLanguagePromptConfig, LANGUAGE_PROMPTS } from '../src/config/prompts';

describe('Language Prompt Configuration', () => {
  it('should return Chinese config for zh-CN', () => {
    const config = getLanguagePromptConfig('zh-CN');
    expect(config.targetLanguage).toBe('中文');
    expect(config.prompt).toContain('你是一名专业的英语学习助手');
  });

  it('should return English config for en-US', () => {
    const config = getLanguagePromptConfig('en-US');
    expect(config.targetLanguage).toBe('English');
    expect(config.prompt).toContain('You are a professional language learning assistant');
  });

  it('should return Japanese config for ja-JP', () => {
    const config = getLanguagePromptConfig('ja-JP');
    expect(config.targetLanguage).toBe('日本語');
    expect(config.prompt).toContain('あなたは翻訳と文法解説に精通したプロの言語学習アシスタント');
  });

  it('should return Korean config for ko-KR', () => {
    const config = getLanguagePromptConfig('ko-KR');
    expect(config.targetLanguage).toBe('한국어');
    expect(config.prompt).toContain('당신은 번역과 문법 설명에 능숙한 전문 언어 학습 도우미');
  });

  it('should handle Accept-Language with quality values', () => {
    const config = getLanguagePromptConfig('zh-CN,zh;q=0.9,en;q=0.8');
    expect(config.targetLanguage).toBe('中文');
  });

  it('should match language prefix when exact match not found', () => {
    const config = getLanguagePromptConfig('zh-TW,zh;q=0.9');
    expect(config.targetLanguage).toBe('中文'); // Should match zh-CN
  });

  it('should return default English config for unknown language', () => {
    const config = getLanguagePromptConfig('fr-FR');
    expect(config.targetLanguage).toBe('English');
  });

  it('should return default English config for undefined Accept-Language', () => {
    const config = getLanguagePromptConfig(undefined);
    expect(config.targetLanguage).toBe('English');
  });

  it('should return default English config for empty Accept-Language', () => {
    const config = getLanguagePromptConfig('');
    expect(config.targetLanguage).toBe('English');
  });

  it('should have all required language configurations', () => {
    expect(LANGUAGE_PROMPTS['zh-CN']).toBeDefined();
    expect(LANGUAGE_PROMPTS['en-US']).toBeDefined();
    expect(LANGUAGE_PROMPTS['ja-JP']).toBeDefined();
    expect(LANGUAGE_PROMPTS['ko-KR']).toBeDefined();
  });
});
