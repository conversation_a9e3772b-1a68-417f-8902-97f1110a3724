import { Hono } from "hono";
import { cors } from "hono/cors";
import type { Context } from "hono";


// import type { Env } from "./types/env";
import { aiHand<PERSON> } from "./handlers/ai";

import { authRoutes } from "./routes/auth.routes";
import { bookRoutes } from './routes/book.routes';
import { categoryRoutes } from './routes/category.routes';
import { docsRouter } from './routes/docs.routes';
import { homeRoutes } from './routes/home.routes';
import { userRoutes } from './routes/user.routes';
import { r2Routes } from './routes/r2.routes';

const app = new Hono<{ Bindings: Env }>();

// 允许跨域中间件
app.use('*', cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'Accept-Language'],
  }));

app.post('/api/v1/chat/ask', aiHandler.generateText);
app.route('/api/v1/auth', authRoutes);
app.route('/api/v1/book', bookRoutes);
app.route('/api/v1/category', categoryRoutes);
app.route('/api/v1/docs', docsRouter);
app.route('/api/v1/home', homeRoutes);
app.route('/api/v1/user', userRoutes);
app.route('/api/v1/r2', r2Routes);

app.get('/', (c) => c.text('Hello Worldddd'));

export { app };