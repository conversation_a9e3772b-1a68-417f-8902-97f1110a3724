import { Context } from 'hono';
import { R2Service } from '../services/r2.service';
import { ResponseUtil } from '../types/response';

export class R2Handler {
    private r2Service: R2Service;

    constructor() {
        this.r2Service = new R2Service();
    }

    /**
     * 获取 presigned URL - 需要用户登录
     */
    getPresignedURL = async (c: Context<{ Bindings: Env }>) => {
        try {
            console.log('请求获取 presigned URL');
            const body = await c.req.json();
            console.log('请求参数:', JSON.stringify(body, null, 2));

            const { fileName, contentType, folder } = body;

            // 从 Context 中获取用户ID（身份验证中间件已经设置）
            const userId = c.get('userId');
            if (!userId) {
                console.log('用户未登录，拒绝请求');
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }

            // 验证参数
            if (!fileName || typeof fileName !== 'string') {
                return c.json(ResponseUtil.badRequest("文件名(fileName)不能为空"), 400);
            }

            if (!contentType || typeof contentType !== 'string') {
                return c.json(ResponseUtil.badRequest("文件类型(contentType)不能为空"), 400);
            }

            // folder 是可选的，如果没有提供则使用 'uploads'
            const uploadFolder = folder || 'uploads';

            const result = await this.r2Service.getPresignedURL(c, userId, uploadFolder, fileName, contentType);
            
            console.log(`成功为用户 ${userId} 生成 presigned URL:`, result.key);
            return c.json(ResponseUtil.success(result, "获取上传链接成功"));
        } catch (error) {
            console.error('获取 presigned URL 失败:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }

            if (errorMessage.includes('不能为空')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }

            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };
}