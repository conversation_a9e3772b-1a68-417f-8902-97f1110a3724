import { Context } from 'hono';
import { UserService } from '../services/user.service';
import { ResponseUtil } from '../types/response';

export class UserHandler {
    private userService: UserService;

    constructor() {
        this.userService = new UserService();
    }

    /// 用户收藏书籍
    addFavorite = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { bookId, progress } = body;
            
            // 验证参数
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }
            
            if (typeof progress !== 'number' || !Number.isInteger(progress) || progress < 0) {
                return c.json(ResponseUtil.badRequest("进度必须是非负整数"), 400);
            }
            
            const result = await this.userService.addFavorite(c, bookId, progress);
            return c.json(ResponseUtil.success(result, "收藏书籍成功"));
        } catch (error) {
            console.error('Add favorite error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('书籍不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            if (errorMessage.includes('书籍ID不能为空') || errorMessage.includes('进度必须是正整数')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 用户删除收藏书籍
    deleteFavorite = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { bookId } = body;
            
            // 验证参数
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }
            
            const result = await this.userService.deleteFavorite(c, bookId);
            return c.json(ResponseUtil.success(result, "收藏删除成功"));
        } catch (error) {
            console.error('Delete favorite error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('书籍不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            if (errorMessage.includes('您没有收藏过这本书')) {
                return c.json(ResponseUtil.badRequest("您没有收藏过这本书"), 400);
            }
            
            if (errorMessage.includes('书籍ID不能为空')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 检查用户是否收藏过指定书籍
    checkFavorite = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { bookId } = body;
            
            // 验证参数
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }
            
            const result = await this.userService.checkFavorite(c, bookId);
            return c.json(ResponseUtil.success(result, "检查收藏状态成功"));
        } catch (error) {
            console.error('Check favorite error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('书籍不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            if (errorMessage.includes('书籍ID不能为空')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 获取用户的收藏列表
    getFavoriteList = async (c: Context<{ Bindings: Env }>) => {
        try {
            // 获取分页参数
            const pageNo = parseInt(c.req.query('pageNo') || '1');
            const pageSize = parseInt(c.req.query('pageSize') || '20');
            
            // 验证分页参数
            if (isNaN(pageNo) || pageNo < 1) {
                return c.json(ResponseUtil.badRequest("页码必须大于等于1"), 400);
            }
            if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) {
                return c.json(ResponseUtil.badRequest("每页大小必须在1-100之间"), 400);
            }
            
            const result = await this.userService.getFavoriteList(c, pageNo, pageSize);
            return c.json(ResponseUtil.success(result, "获取收藏列表成功"));
        } catch (error) {
            console.error('Get favorite list error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('页码必须大于等于1') || errorMessage.includes('每页大小必须在1-100之间')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 获取用户的历史阅读列表
    getHistoryList = async (c: Context<{ Bindings: Env }>) => {
        try {
            // 获取分页参数
            const pageNo = parseInt(c.req.query('pageNo') || '1');
            const pageSize = parseInt(c.req.query('pageSize') || '20');
            
            // 验证分页参数
            if (isNaN(pageNo) || pageNo < 1) {
                return c.json(ResponseUtil.badRequest("页码必须大于等于1"), 400);
            }
            if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) {
                return c.json(ResponseUtil.badRequest("每页大小必须在1-100之间"), 400);
            }
            
            const result = await this.userService.getHistoryList(c, pageNo, pageSize);
            return c.json(ResponseUtil.success(result, "获取历史阅读列表成功"));
        } catch (error) {
            console.error('Get history list error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('页码必须大于等于1') || errorMessage.includes('每页大小必须在1-100之间')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /**
     * 获取用户信息
     * 对应Java项目中的UserController.getUserInfo()
     */
    getUserInfo = async (c: Context<{ Bindings: Env }>) => {
        try {
            // 从 Context 中获取用户ID（身份验证中间件已经设置）
            const userId = c.get('userId');
            if (!userId) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }

            const userInfo = await this.userService.getUserInfo(c, userId);
            return c.json(ResponseUtil.success(userInfo, "获取用户信息成功"));
        } catch (error) {
            console.error('Get user info error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('用户不存在')) {
                return c.json(ResponseUtil.notFound("用户不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

  
    /// 更新用户头像
    updateAvatar = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { avatarUrl } = body;

            // 从 Context 中获取用户ID（身份验证中间件已经设置）
            const userId = c.get('userId');
            if (!userId) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }

            // 验证参数
            if (!avatarUrl) {
                return c.json(ResponseUtil.badRequest("头像URL不能为空"), 400);
            }

            const result = await this.userService.updateAvatar(c, userId, avatarUrl);
            return c.json(ResponseUtil.success(result, "更新头像成功"));
        } catch (error) {
            console.error('Update avatar error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }

            if (errorMessage.includes('用户不存在')) {
                return c.json(ResponseUtil.notFound("用户不存在"), 404);
            }

            if (errorMessage.includes('头像URL不能为空')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }

            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };
}
