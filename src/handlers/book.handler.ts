import { Context } from 'hono';
import { BookService } from '../services/book.service';
import { ResponseUtil } from '../types/response';

export class BookHandler {
    private bookService: BookService;

    constructor() {
        this.bookService = new BookService();
    }

    /// 上传书籍
    uploadBook = async (c: Context<{ Bindings: Env }>) => {
        try {
            const formData = await c.req.formData();
            const result = await this.bookService.uploadBook(c, formData);
            return c.json(ResponseUtil.success(result, "书籍上传成功"));
        } catch (error) {
            console.error('Upload book error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 获取书籍列表
    getBookList = async (c: Context<{ Bindings: Env }>) => {
        try {
            const page = parseInt(c.req.query('page') || '1');
            const pageSize = parseInt(c.req.query('pageSize') || '10');
            
            const books = await this.bookService.getBookList(c, page, pageSize);
            return c.json(ResponseUtil.success(books, "获取书籍列表成功"));
        } catch (error) {
            console.error('Get book list error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 获取书籍URL
    getBookUrl = async (c: Context<{ Bindings: Env }>) => {
        try {
            const bookId = c.req.param('id');
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }

            const url = await this.bookService.getBookUrl(c, bookId);
            return c.json(ResponseUtil.success(url, "获取书籍URL成功"));
        } catch (error) {
            console.error('Get book URL error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('not found')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 编辑书籍
    updateBook = async (c: Context<{ Bindings: Env }>) => {
        try {
            const bookId = c.req.param('id');
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }

            const formData = await c.req.formData();
            const result = await this.bookService.updateBook(c, bookId, formData);
            return c.json(ResponseUtil.success(result, "书籍更新成功"));
        } catch (error) {
            console.error('Update book error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 删除书籍
    deleteBook = async (c: Context<{ Bindings: Env }>) => {
        try {
            const bookId = c.req.param('id');
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }

            const result = await this.bookService.deleteBook(c, bookId);
            return c.json(ResponseUtil.success(result, "书籍删除成功"));
        } catch (error) {
            console.error('Delete book error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 用户为书籍评分
    rateBook = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { bookId, score } = body;
            
            // 验证参数
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }
            
            if (typeof score !== 'number' || score < 1 || score > 5) {
                return c.json(ResponseUtil.badRequest("评分必须是1-5之间的数字"), 400);
            }
            
            const result = await this.bookService.rateBook(c, bookId, score);
            return c.json(ResponseUtil.success(result, "评分成功"));
        } catch (error) {
            console.error('Rate book error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('书籍不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            if (errorMessage.includes('评分必须在1-5之间')) {
                return c.json(ResponseUtil.badRequest("评分必须在1-5之间"), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 更新用户阅读进度
    updateReadProgress = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { bookId, progress } = body;
            
            // 验证参数
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }
            
            if (typeof progress !== 'number' || !Number.isInteger(progress) || progress < 0) {
                return c.json(ResponseUtil.badRequest("进度必须是正数"), 400);
            }
            
            const result = await this.bookService.updateReadProgress(c, bookId, progress);
            return c.json(ResponseUtil.success(result, "阅读进度更新成功"));
        } catch (error) {
            console.error('Update read progress error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('书籍不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            if (errorMessage.includes('进度必须是正整数')) {
                return c.json(ResponseUtil.badRequest("进度必须是正整数"), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 根据分类获取书籍列表
    getBooksByCategory = async (c: Context<{ Bindings: Env }>) => {
        try {
            const categoryId = c.req.query('categoryId');
            
            // 验证参数
            if (!categoryId) {
                return c.json(ResponseUtil.badRequest("分类ID不能为空"), 400);
            }
            
            // 获取分页参数
            const pageNo = parseInt(c.req.query('pageNo') || '1');
            const pageSize = parseInt(c.req.query('pageSize') || '20');
            
            // 验证分页参数
            if (isNaN(pageNo) || pageNo < 1) {
                return c.json(ResponseUtil.badRequest("页码必须大于等于1"), 400);
            }
            if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) {
                return c.json(ResponseUtil.badRequest("每页大小必须在1-100之间"), 400);
            }
            
            const result = await this.bookService.getBooksByCategory(c, categoryId, pageNo, pageSize);
            return c.json(ResponseUtil.success(result, "获取分类书籍列表成功"));
        } catch (error) {
            console.error('Get books by category error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('分类不存在')) {
                return c.json(ResponseUtil.notFound("分类不存在"), 404);
            }
            
            if (errorMessage.includes('页码必须大于等于1') || errorMessage.includes('每页大小必须在1-100之间')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 获取书籍详情
    getBookDetail = async (c: Context<{ Bindings: Env }>) => {
        try {
            const bookId = c.req.param('id');
            
            // 验证参数
            if (!bookId) {
                return c.json(ResponseUtil.badRequest("书籍ID不能为空"), 400);
            }
            
            const result = await this.bookService.getBookDetail(c, bookId);
            return c.json(ResponseUtil.success(result, "获取书籍详情成功"));
        } catch (error) {
            console.error('Get book detail error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('书籍不存在')) {
                return c.json(ResponseUtil.notFound("书籍不存在"), 404);
            }
            
            if (errorMessage.includes('书籍ID不能为空')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };
}
