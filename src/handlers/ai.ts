import {
    GoogleGenAI,
} from '@google/genai';
import { Context } from 'hono';
import { getLanguagePromptConfig } from '../config/prompts';

const genai = new GoogleGenAI({
    apiKey: "AIzaSyD2E8EsEpzAxRp-BucFgGkf1bojNjg2R24",
});

export const aiHandler = {
    async generateText(ctx: Context<{ Bindings: Env }>) {
        try {
            // 从请求体中获取用户输入
            const { prompt } = await ctx.req.json();

            console.log('User prompt:', prompt);

            // 从请求头中获取 Accept-Language
            const acceptLanguage = ctx.req.header('Accept-Language');
            console.log('Accept-Language:', acceptLanguage);

            // 根据语言配置获取对应的 prompt 模板
            const languageConfig = getLanguagePromptConfig(acceptLanguage);
            console.log('Selected language config:', languageConfig.targetLanguage);

            const aiPrompt = `${languageConfig.prompt}

用户输入：${prompt}`;
            
            if (!prompt) {
                return ctx.json({
                    success: false,
                    message: aiPrompt,
                });
            }

            // 创建一个 ReadableStream
            const stream = new ReadableStream({
                async start(controller) {
                    try {
                        const response = await genai.models.generateContentStream({
                            model: 'gemini-2.0-flash',
                            contents: [{
                                parts: [{ text: aiPrompt }],
                            }],
                        });

                        // 处理每个文本块
                        for await (const chunk of response) {
                            if (chunk.text) {
                                const data = JSON.stringify({ text: chunk.text });
                                controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`));
                            }
                        }
                    } catch (error) {
                        console.error('Gemini API 错误:', error);
                        const errorData = JSON.stringify({ 
                            error: error instanceof Error ? error.message : String(error) 
                        });
                        controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`));
                    } finally {
                        controller.close();
                    }
                },
                cancel() {
                    // 处理取消事件
                    console.log('Stream was canceled by the client');
                }
            });

            // 立即返回响应
            return new Response(stream, {
                headers: {
                    'Content-Type': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                },
            });
        } catch (error) {
            console.error('请求处理错误:', error);
            return ctx.json({
                success: false,
                message: '生成内容失败',
                error: error instanceof Error ? error.message : String(error),
            }, 500);
        }
    },
};
