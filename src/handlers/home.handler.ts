import { Context } from 'hono';
import { HomeService } from '../services/home.service';
import { ResponseUtil } from '../types/response';

export class HomeHandler {
	private homeService: HomeService;

	constructor() {
		this.homeService = new HomeService();
	}

	/// 获取游客Banner书籍列表
	getGuestBanner = async (c: Context<{ Bindings: Env }>) => {
		try {
			const books = await this.homeService.getGuestBanner(c);
			console.log('Guest banner books:', books);
			return c.json(ResponseUtil.success(books, '获取Banner书籍列表成功'));
		} catch (error) {
			console.error('Get guest banner error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			return c.json(ResponseUtil.error(errorMessage), 500);
		}
	};

	/// 获取游客推荐书籍列表
	getGuestRecommend = async (c: Context<{ Bindings: Env }>) => {
		try {
			const books = await this.homeService.getGuestRecommend(c);
			console.log('Guest recommend books:', books);
			return c.json(ResponseUtil.success(books, '获取推荐书籍列表成功'));
		} catch (error) {
			console.error('Get guest recommend error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			return c.json(ResponseUtil.error(errorMessage), 500);
		}
	};

	/// 获取Banner书籍列表（阅读次数最多的3条）
	getBanner = async (c: Context<{ Bindings: Env }>) => {
		try {
			const books = await this.homeService.getBanner(c);
			console.log('Banner books:', books);
			return c.json(ResponseUtil.success(books, '获取Banner书籍列表成功'));
		} catch (error) {
			console.error('Get banner error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			return c.json(ResponseUtil.error(errorMessage), 500);
		}
	};

	/// 获取推荐书籍列表（喜欢次数最多的3条）
	getRecommend = async (c: Context<{ Bindings: Env }>) => {
		try {
			const books = await this.homeService.getRecommend(c);
			return c.json(ResponseUtil.success(books, '获取推荐书籍列表成功'));
		} catch (error) {
			console.error('Get recommend error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			return c.json(ResponseUtil.error(errorMessage), 500);
		}
	};

	/// 获取用户最近阅读记录
	getRecentRead = async (c: Context<{ Bindings: Env }>) => {
		try {
			const books = await this.homeService.getRecentRead(c);
			return c.json(ResponseUtil.success(books, '获取最近阅读记录成功'));
		} catch (error) {
			console.error('Get recent read error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			return c.json(ResponseUtil.error(errorMessage), 500);
		}
	};
}
