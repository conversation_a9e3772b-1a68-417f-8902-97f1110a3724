import { Context } from 'hono';
import { CategoryService } from '../services/category.service';
import { ResponseUtil } from '../types/response';

export class CategoryHandler {
    private categoryService: CategoryService;

    constructor() {
        this.categoryService = new CategoryService();
    }

    /// 新增分类
    createCategory = async (c: Context<{ Bindings: Env }>) => {
        try {
            const formData = await c.req.formData();
            const result = await this.categoryService.createCategory(c, formData);
            return c.json(ResponseUtil.success(result, "分类创建成功"));
        } catch (error) {
            console.error('Create category error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 修改分类
    updateCategory = async (c: Context<{ Bindings: Env }>) => {
        try {
            const categoryId = c.req.param('id');
            if (!categoryId) {
                return c.json(ResponseUtil.badRequest("分类ID不能为空"), 400);
            }

            const formData = await c.req.formData();
            const result = await this.categoryService.updateCategory(c, categoryId, formData);
            return c.json(ResponseUtil.success(result, "分类更新成功"));
        } catch (error) {
            console.error('Update category error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('不存在')) {
                return c.json(ResponseUtil.notFound("分类不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 删除分类
    deleteCategory = async (c: Context<{ Bindings: Env }>) => {
        try {
            const categoryId = c.req.param('id');
            if (!categoryId) {
                return c.json(ResponseUtil.badRequest("分类ID不能为空"), 400);
            }

            const result = await this.categoryService.deleteCategory(c, categoryId);
            return c.json(ResponseUtil.success(result, "分类删除成功"));
        } catch (error) {
            console.error('Delete category error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('不存在')) {
                return c.json(ResponseUtil.notFound("分类不存在"), 404);
            }
            
            if (errorMessage.includes('还有书籍')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 查询所有分类
    getAllCategories = async (c: Context<{ Bindings: Env }>) => {
        try {
            const categories = await this.categoryService.getAllCategories(c);
            return c.json(ResponseUtil.success(categories, "获取分类列表成功"));
        } catch (error) {
            console.error('Get all categories error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 根据ID查询分类
    getCategoryById = async (c: Context<{ Bindings: Env }>) => {
        try {
            const categoryId = c.req.param('id');
            if (!categoryId) {
                return c.json(ResponseUtil.badRequest("分类ID不能为空"), 400);
            }

            const category = await this.categoryService.getCategoryById(c, categoryId);
            return c.json(ResponseUtil.success(category, "获取分类信息成功"));
        } catch (error) {
            console.error('Get category by id error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('不存在')) {
                return c.json(ResponseUtil.notFound("分类不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };
} 