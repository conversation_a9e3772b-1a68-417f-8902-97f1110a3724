import { Context } from "hono";
import { nanoid } from "nanoid";
import { AuthService } from "../services/auth.service";
import { ResponseUtil } from "../types/response";
import type { AppleAuthDTO } from "../types/auth.types";

export class AuthHandler {
    constructor(private authService: AuthService) {
    }

    foo = async (ctx: Context<{ Bindings: Env }>) => {
        const result = this.authService.foo();
        const upperCaseResult = this.upperCase(result);
        console.log('result', result, upperCaseResult);
        return ctx.json({ result, upperCaseResult });
    }

    login = async (ctx: Context<{ Bindings: Env }>) => {
        try {
            const { appleAuthId } = await ctx.req.json();
            const token = await this.authService.login(ctx, appleAuthId);
            return ctx.json({ token, appleAuthId });
        } catch (error) {
            console.error('Error in login:', error);
            return ctx.json({ error: 'Internalddd server error' }, 500);
        }
    }

    async logout(ctx: Context<{ Bindings: Env }>) {
        return ctx.json({ token: nanoid() });
    }

    /// 获取用户信息
    getUserInfo = async (c: Context<{ Bindings: Env }>) => {
        try {
            const result = await this.authService.getUserInfo(c);
            return c.json(ResponseUtil.success(result, "获取用户信息成功"));
        } catch (error) {
            console.error('Get user info error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('用户不存在')) {
                return c.json(ResponseUtil.notFound("用户不存在"), 404);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    };

    /// 更新用户信息
    updateUserInfo = async (c: Context<{ Bindings: Env }>) => {
        try {
            const body = await c.req.json();
            const { name, avatar } = body;
            
            // 验证参数
            if (name !== undefined && (typeof name !== 'string' || name.trim() === '')) {
                return c.json(ResponseUtil.badRequest("姓名不能为空"), 400);
            }
            
            if (avatar !== undefined && (typeof avatar !== 'string' || avatar.trim() === '')) {
                return c.json(ResponseUtil.badRequest("头像URL不能为空"), 400);
            }
            
            const result = await this.authService.updateUserInfo(c, name, avatar);
            return c.json(ResponseUtil.success(result, "用户信息更新成功"));
        } catch (error) {
            console.error('Update user info error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            if (errorMessage.includes('用户未登录')) {
                return c.json(ResponseUtil.error("用户未登录", 401), 401);
            }
            
            if (errorMessage.includes('用户不存在')) {
                return c.json(ResponseUtil.notFound("用户不存在"), 404);
            }
            
            if (errorMessage.includes('姓名不能为空') || errorMessage.includes('头像URL不能为空')) {
                return c.json(ResponseUtil.badRequest(errorMessage), 400);
            }
            
            return c.json(ResponseUtil.error(errorMessage), 500);
        }
    }

    /**
     * Apple 登录处理方法
     * 对应 Java 项目中的 AuthController.verifyAppleToken 方法
     */
    appleLogin = async (ctx: Context<{ Bindings: Env }>) => {
        try {
            // 解析请求体 - 对应 Java 中的 @RequestBody AppleAuthDTO appleAuthDTO
            const body = await ctx.req.json() as AppleAuthDTO;
            
            console.log('Apple login request received:', {
                hasIdentityToken: !!body.identityToken,
                givenName: body.givenName,
                familyName: body.familyName,
                hasEmail: !!body.email
            });
            
            // 验证必要参数
            if (!body.identityToken) {
                return ctx.json(ResponseUtil.badRequest("Identity token is required"), 400);
            }

            // 调用 AuthService 的 Apple 登录方法 - 对应 Java 中的 userService.loginWithApple(appleAuthDTO)
            const authVO = await this.authService.loginWithApple(ctx, body);
            
            console.log('Apple login successful:', {
                userId: authVO.userId,
                isNew: authVO.isNew
            });
            
            // 返回成功结果 - 对应 Java 中的 return ApiBaseDTO.ok(authVO)
            return ctx.json(ResponseUtil.success(authVO, "Apple login successful"));
        } catch (error) {
            console.error('Apple login error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            // 根据错误类型返回不同的状态码
            if (errorMessage.includes('Apple 登录失败')) {
                if (errorMessage.includes('Invalid issuer') || 
                    errorMessage.includes('Invalid audience') || 
                    errorMessage.includes('expired')) {
                    return ctx.json(ResponseUtil.badRequest(errorMessage), 400);
                }
            }
            
            if (errorMessage.includes('not configured')) {
                return ctx.json(ResponseUtil.error("Server configuration error"), 500);
            }
            
            // 默认错误响应 - 对应 Java 中的异常处理
            return ctx.json(ResponseUtil.error("Apple login failed"), 500);
        }
    };

    private upperCase(str: string) {
        const bar = this.authService.foo();
        console.log('bar', bar);
        return str.toUpperCase();
    }
}