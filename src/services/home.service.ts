import { Context } from 'hono';
import { drizzle } from 'drizzle-orm/d1';
import { inArray, desc, eq } from 'drizzle-orm';
import { book } from '../db/schema/book';
import { history } from '../db/schema/history';
import { rating } from '../db/schema/rating';
import { DateUtil } from '../utils/dateUtil';

export class HomeService {
	constructor() {}

	/// 计算书籍评分
	private calculateBookScore(ratings: any[]) {
		if (!ratings || ratings.length === 0) {
			return {
				score: null,
				ratingCount: 0,
				hasRating: false,
			};
		}

		const totalScore = ratings.reduce((sum, r) => sum + r.score, 0);
		const avgScore = totalScore / ratings.length;

		return {
			score: Math.round(avgScore * 10) / 10, // 保留一位小数
			ratingCount: ratings.length,
			hasRating: true,
		};
	}

	/// 获取用户对书籍的阅读进度信息
	private async getUserBookProgress(db: any, userId: string, bookIds: string[]) {
		if (!userId || bookIds.length === 0) {
			return new Map();
		}

		const userHistory = await db.select().from(history).where(inArray(history.bookId, bookIds)).all();

		const progressMap = new Map();
		userHistory.forEach((record: any) => {
			progressMap.set(record.bookId, {
				lastViewedTime: DateUtil.formatToCustomDateTime(record.lastViewedTime),
				progress: record.progress,
			});
		});

		return progressMap;
	}

	/// 从KV中获取游客书籍列表（可重用）
	private async getGuestBooksFromKV(ctx: Context<{ Bindings: Env }>, key: string) {
		try {
			// 第一步：从KV获取书籍ID数组
			// const json = await ctx.env.KR_CONFIG.get(key);
			// TODO 测试数据
			const json = JSON.stringify(['iskTMDF-9syew6eQxbSqc']);
			console.log(`Fetched data from KV for key ${key}:`, json);
			if (!json) {
				console.log(`${key} is empty in KV`);
				// TODO
				return [];
			}

			const bookIds = JSON.parse(json) as string[];
			console.log(`Guest book IDs from ${key}:`, bookIds);

			if (!Array.isArray(bookIds) || bookIds.length === 0) {
				console.log(`No book IDs found in ${key}`);
				return [];
			}

			// 第二步：根据ID数组查询book表
			const db = drizzle(ctx.env.DB);
			const books = await db.select().from(book).where(inArray(book.id, bookIds)).all();

			console.log(`Found ${books.length} books for ${key}`);

			// 第三步：按照KV中的顺序返回
			const sortedBooks = bookIds.map((id) => books.find((b) => b.id === id)).filter(Boolean); // 过滤掉不存在的书籍

			console.log(`Returning ${sortedBooks.length} sorted books for ${key}`);
			return sortedBooks;
		} catch (error) {
			console.error(`Error getting guest books from KV with key ${key}:`, error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			throw new Error(`Failed to get guest books from KV: ${errorMessage}`);
		}
	}

	/// 获取游客Banner书籍列表
	async getGuestBanner(ctx: Context<{ Bindings: Env }>) {
		return this.getGuestBooksFromKV(ctx, 'GUEST_BANNER');
	}

	/// 获取游客推荐书籍列表
	async getGuestRecommend(ctx: Context<{ Bindings: Env }>) {
		return this.getGuestBooksFromKV(ctx, 'GUEST_RECOMMEND');
	}

	/// 获取Banner书籍列表（阅读次数最多的3条）
	async getBanner(ctx: Context<{ Bindings: Env }>) {
		try {
			const db = drizzle(ctx.env.DB);

			// 从Context中获取用户ID
			const userId = ctx.get('userId');

			// 查询阅读次数最多的3条数据，按readCount降序排列
			const books = await db.select().from(book).orderBy(desc(book.readCount)).limit(3).all();

			console.log(`Found ${books.length} books for banner (top read count)`);

			if (books.length === 0) {
				return [];
			}

			// 获取书籍ID列表
			const bookIds = books.map((b) => b.id);

			// 获取用户阅读进度信息
			const progressMap = await this.getUserBookProgress(db, userId, bookIds);

			// 获取评分信息
			const ratings = await db.select().from(rating).where(inArray(rating.bookId, bookIds)).all();

			// 按书籍ID分组评分
			const ratingsMap = new Map();
			ratings.forEach((r) => {
				if (!ratingsMap.has(r.bookId)) {
					ratingsMap.set(r.bookId, []);
				}
				ratingsMap.get(r.bookId).push(r);
			});

			// 组装返回数据
			const result = books.map((book) => {
				const bookProgress = progressMap.get(book.id);
				const bookRatings = ratingsMap.get(book.id) || [];
				const scoreInfo = this.calculateBookScore(bookRatings);

				return {
					...book,
					lastViewedTime: bookProgress?.lastViewedTime || null,
					progress: bookProgress?.progress || 0,
					...scoreInfo,
				};
			});

			console.log(`Returning ${result.length} books with progress and rating info`);
			return result;
		} catch (error) {
			console.error('Error getting banner:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			throw new Error(`Failed to get banner: ${errorMessage}`);
		}
	}

	/// 获取推荐书籍列表（喜欢次数最多的3条）
	async getRecommend(ctx: Context<{ Bindings: Env }>) {
		try {
			const db = drizzle(ctx.env.DB);

			// 从Context中获取用户ID
			const userId = ctx.get('userId');

			// 查询喜欢次数最多的3条数据，按favoriteCount降序排列
			const books = await db.select().from(book).orderBy(desc(book.favoriteCount)).limit(3).all();

			console.log(`Found ${books.length} books for recommend (top favorite count)`);

			if (books.length === 0) {
				return [];
			}

			// 获取书籍ID列表
			const bookIds = books.map((b) => b.id);

			// 获取用户阅读进度信息
			const progressMap = await this.getUserBookProgress(db, userId, bookIds);

			// 获取评分信息
			const ratings = await db.select().from(rating).where(inArray(rating.bookId, bookIds)).all();

			// 按书籍ID分组评分
			const ratingsMap = new Map();
			ratings.forEach((r) => {
				if (!ratingsMap.has(r.bookId)) {
					ratingsMap.set(r.bookId, []);
				}
				ratingsMap.get(r.bookId).push(r);
			});

			// 组装返回数据
			const result = books.map((book) => {
				const bookProgress = progressMap.get(book.id);
				const bookRatings = ratingsMap.get(book.id) || [];
				const scoreInfo = this.calculateBookScore(bookRatings);

				return {
					...book,
					lastViewedTime: bookProgress?.lastViewedTime || null,
					progress: bookProgress?.progress || 0,
					...scoreInfo,
				};
			});

			console.log(`Returning ${result.length} books with progress and rating info`);
			return result;
		} catch (error) {
			console.error('Error getting recommend:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			throw new Error(`Failed to get recommend: ${errorMessage}`);
		}
	}

	/// 获取用户最近阅读记录（最近3条）
	async getRecentRead(ctx: Context<{ Bindings: Env }>) {
		try {
			const db = drizzle(ctx.env.DB);

			// 从Context中获取用户ID
			const userId = ctx.get('userId');
			if (!userId) {
				console.log('No user ID found in context');
				return [];
			}

			console.log('Getting recent read for user:', userId);

			// 查询用户最近3条阅读记录，按lastViewedTime降序排列
			const recentHistory = await db
				.select()
				.from(history)
				.where(eq(history.userId, userId))
				.orderBy(desc(history.lastViewedTime))
				.limit(3)
				.all();

			console.log(`Found ${recentHistory.length} recent read records`);

			if (recentHistory.length === 0) {
				return [];
			}

			// 获取对应的书籍详情
			const bookIds = recentHistory.map((h) => h.bookId).filter(Boolean) as string[];
			const books = await db.select().from(book).where(inArray(book.id, bookIds)).all();

			// 获取评分信息
			const ratings = await db.select().from(rating).where(inArray(rating.bookId, bookIds)).all();

			// 按书籍ID分组评分
			const ratingsMap = new Map();
			ratings.forEach((r) => {
				if (!ratingsMap.has(r.bookId)) {
					ratingsMap.set(r.bookId, []);
				}
				ratingsMap.get(r.bookId).push(r);
			});

			// 按照阅读记录的顺序返回书籍，并添加阅读进度信息和评分
			const result = recentHistory
				.map((record) => {
					const bookInfo = books.find((b) => b.id === record.bookId);
					if (bookInfo) {
						const bookRatings = ratingsMap.get(record.bookId) || [];
						const scoreInfo = this.calculateBookScore(bookRatings);

						return {
							...bookInfo,
							progress: record.progress,
							lastViewedTime: DateUtil.formatToCustomDateTime(record.lastViewedTime),
							...scoreInfo,
						};
					}
					return null;
				})
				.filter(Boolean);

			console.log(`Returning ${result.length} books with recent read info`);
			return result;
		} catch (error) {
			console.error('Error getting recent read:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			throw new Error(`Failed to get recent read: ${errorMessage}`);
		}
	}
}
