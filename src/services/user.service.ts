import { Context } from "hono";
import { drizzle } from 'drizzle-orm/d1';
import { eq, and, sql } from 'drizzle-orm';
import { favorite } from "../db/schema/favorite";
import { history } from "../db/schema/history";
import { book } from "../db/schema/book";
import { user } from "../db/schema/user";
import { nanoid } from "nanoid";
import { UserVO } from "../types/user.types";
import { DateUtil } from "../utils/dateUtil";

export class UserService {
    constructor() { }

    /// 用户收藏书籍
    async addFavorite(ctx: Context<{ Bindings: Env }>, bookId: string, progress: number) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证参数
        if (!bookId) {
            throw new Error('书籍ID不能为空');
        }
        
        if (typeof progress !== 'number' || !Number.isInteger(progress) || progress < 0) {
            throw new Error('进度必须大于等于0');
        }
        
        try {
            // 检查书籍是否存在
            const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
            if (!existingBook) {
                throw new Error('书籍不存在');
            }
            
            // 检查用户是否已经收藏过这本书
            const existingFavorite = await db.select().from(favorite)
                .where(and(eq(favorite.userId, userId), eq(favorite.bookId, bookId)))
                .get();
            
            if (existingFavorite) {
                // 更新现有收藏记录
                console.log(`Updating existing favorite for user ${userId}, book ${bookId}, progress ${progress}`);
                const updatedFavorite = await db.update(favorite)
                    .set({
                        progress,
                        lastViewedTime: new Date(),
                    })
                    .where(and(eq(favorite.userId, userId), eq(favorite.bookId, bookId)))
                    .returning();
                
                console.log('Favorite updated successfully:', updatedFavorite);
                return updatedFavorite;
            } else {
                // 创建新收藏记录
                const favoriteId = nanoid();
                console.log(`Creating new favorite for user ${userId}, book ${bookId}, progress ${progress}`);
                const newFavorite = await db.insert(favorite).values({
                    id: favoriteId,
                    userId,
                    bookId,
                    progress,
                    lastViewedTime: new Date(),
                }).returning();
                
                console.log('Favorite created successfully:', newFavorite);
                return newFavorite;
            }
        } catch (error) {
            console.error('Add favorite error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`收藏书籍失败: ${errorMessage}`);
        }
    }

    /// 用户删除收藏书籍
    async deleteFavorite(ctx: Context<{ Bindings: Env }>, bookId: string) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证参数
        if (!bookId) {
            throw new Error('书籍ID不能为空');
        }
        
        try {
            // 检查书籍是否存在
            const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
            if (!existingBook) {
                throw new Error('书籍不存在');
            }
            
            // 检查用户是否收藏过这本书
            const existingFavorite = await db.select().from(favorite)
                .where(and(eq(favorite.userId, userId), eq(favorite.bookId, bookId)))
                .get();
            
            if (!existingFavorite) {
                throw new Error('您没有收藏过这本书');
            }
            
            // 删除收藏记录
            console.log(`Deleting favorite for user ${userId}, book ${bookId}`);
            await db.delete(favorite)
                .where(and(eq(favorite.userId, userId), eq(favorite.bookId, bookId)));
            
            console.log('Favorite deleted successfully');
            return { 
                success: true, 
                message: '收藏删除成功',
                bookId,
                userId
            };
        } catch (error) {
            console.error('Delete favorite error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`删除收藏失败: ${errorMessage}`);
        }
    }

    /// 检查用户是否收藏过指定书籍
    async checkFavorite(ctx: Context<{ Bindings: Env }>, bookId: string) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证参数
        if (!bookId) {
            throw new Error('书籍ID不能为空');
        }
        
        try {
            // 检查书籍是否存在
            const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
            if (!existingBook) {
                throw new Error('书籍不存在');
            }
            
            // 检查用户是否收藏过这本书
            const existingFavorite = await db.select().from(favorite)
                .where(and(eq(favorite.userId, userId), eq(favorite.bookId, bookId)))
                .get();
            
            console.log(`Checking favorite for user ${userId}, book ${bookId}, exists: ${!!existingFavorite}`);
            
            return !!existingFavorite;
        } catch (error) {
            console.error('Check favorite error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`检查收藏状态失败: ${errorMessage}`);
        }
    }

    /// 获取用户的收藏列表
    async getFavoriteList(ctx: Context<{ Bindings: Env }>, pageNo: number = 1, pageSize: number = 20) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证分页参数
        if (pageNo < 1) {
            throw new Error('页码必须大于等于1');
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new Error('每页大小必须在1-100之间');
        }
        
        try {
            // 计算偏移量
            const offset = (pageNo - 1) * pageSize;
            
            // 查询总数
            const totalResult = await db
                .select({ count: sql<number>`count(*)` })
                .from(favorite)
                .where(eq(favorite.userId, userId));
            
            const total = totalResult[0]?.count || 0;
            
            // 查询用户的收藏列表，并关联书籍信息（带分页）
            const favorites = await db
                .select({
                    // favorite 表字段
                    favoriteId: favorite.id,
                    progress: favorite.progress,
                    lastViewedTime: favorite.lastViewedTime,
                    createTime: favorite.createTime,
                    // book 表字段
                    bookId: book.id,
                    title: book.title,
                    cover: book.cover,
                    url: book.url,
                    level: book.level,
                    categoryId: book.categoryId,
                    readCount: book.readCount,
                    favoriteCount: book.favoriteCount,
                    bookCreateTime: book.createTime,
                    bookUpdateTime: book.updateTime,
                    isFree: book.isFree
                })
                .from(favorite)
                .innerJoin(book, eq(favorite.bookId, book.id))
                .where(eq(favorite.userId, userId))
                .orderBy(favorite.createTime)
                .limit(pageSize)
                .offset(offset);
            
            console.log(`Getting favorite list for user ${userId}, page ${pageNo}, size ${pageSize}, found ${favorites.length} favorites`);
            
            // 格式化返回数据，与前端 BookItem 模型匹配
            const formattedFavorites = favorites.map(fav => ({
                id: fav.bookId || '',
                title: fav.title || '',
                cover: fav.cover || '',
                url: fav.url || '',
                level: fav.level || 0,
                categoryId: fav.categoryId?.toString() || '',
                readCount: fav.readCount || 0,
                favoriteCount: fav.favoriteCount || 0,
                isFree: fav.isFree || 0,
                score: null,
                lastViewedTime: DateUtil.formatToCustomDateTime(fav.lastViewedTime),
                progress: fav.progress || 0
            }));
            
            return {
                books: formattedFavorites,
                total,
                totalPages: Math.ceil(total / pageSize)
            };
        } catch (error) {
            console.error('Get favorite list error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`获取收藏列表失败: ${errorMessage}`);
        }
    }

    /// 获取用户的历史阅读列表
    async getHistoryList(ctx: Context<{ Bindings: Env }>, pageNo: number = 1, pageSize: number = 20) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证分页参数
        if (pageNo < 1) {
            throw new Error('页码必须大于等于1');
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new Error('每页大小必须在1-100之间');
        }
        
        try {
            // 计算偏移量
            const offset = (pageNo - 1) * pageSize;
            
            // 查询总数
            const totalResult = await db
                .select({ count: sql<number>`count(*)` })
                .from(history)
                .where(eq(history.userId, userId));
            
            const total = totalResult[0]?.count || 0;
            
            // 查询用户的历史阅读列表，并关联书籍信息（带分页）
            const histories = await db
                .select({
                    // history 表字段
                    historyId: history.id,
                    progress: history.progress,
                    lastViewedTime: history.lastViewedTime,
                    createTime: history.createTime,
                    // book 表字段
                    bookId: book.id,
                    title: book.title,
                    cover: book.cover,
                    url: book.url,
                    level: book.level,
                    categoryId: book.categoryId,
                    readCount: book.readCount,
                    favoriteCount: book.favoriteCount,
                    bookCreateTime: book.createTime,
                    bookUpdateTime: book.updateTime,
                    isFree: book.isFree
                })
                .from(history)
                .innerJoin(book, eq(history.bookId, book.id))
                .where(eq(history.userId, userId))
                .orderBy(history.lastViewedTime)
                .limit(pageSize)
                .offset(offset);
            
            console.log(`Getting history list for user ${userId}, page ${pageNo}, size ${pageSize}, found ${histories.length} histories`);
            
            // 格式化返回数据，与前端 BookItem 模型匹配
            const formattedHistories = histories.map(hist => ({
                id: hist.bookId || '',
                title: hist.title || '',
                cover: hist.cover || '',
                url: hist.url || '',
                level: hist.level || 0,
                categoryId: hist.categoryId?.toString() || '',
                readCount: hist.readCount || 0,
                favoriteCount: hist.favoriteCount || 0,
                isFree: hist.isFree || 0,
                score: null,
                lastViewedTime: DateUtil.formatToCustomDateTime(hist.lastViewedTime),
                progress: hist.progress || 0
            }));

            return {
                books: formattedHistories,
                total,
                totalPages: Math.ceil(total / pageSize)
            };
        } catch (error) {
            console.error('Get history list error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`获取历史阅读列表失败: ${errorMessage}`);
        }
    }

    /**
     * 获取用户信息
     * 对应Java项目中的getUserInfo方法
     * 
     * @param ctx Context对象
     * @param uid 用户ID
     * @returns UserVO用户信息对象
     */
    async getUserInfo(ctx: Context<{ Bindings: Env }>, uid: string): Promise<UserVO> {
        const db = drizzle(ctx.env.DB);
        
        try {
            // 1. 获取用户基本信息
            const userInfo = await db.select().from(user).where(eq(user.id, uid)).get();
            if (!userInfo) {
                throw new Error('用户不存在');
            }

            // 2. 获取阅读记录数量
            const historyCountResult = await db
                .select({ count: sql<number>`count(*)` })
                .from(history)
                .where(eq(history.userId, uid));
            const readCount = historyCountResult[0]?.count || 0;

            // 3. 计算阅读天数（注册到现在的天数）
            const readDay = DateUtil.getDaysBetween(userInfo.createdAt);

            // 4. 获取收藏数量
            const favoriteCountResult = await db
                .select({ count: sql<number>`count(*)` })
                .from(favorite)
                .where(eq(favorite.userId, uid));
            const favoriteCount = favoriteCountResult[0]?.count || 0;

            // 5. 处理头像URL
            const avatar = DateUtil.processAvatarUrl(userInfo.avatar);

            console.log(`Getting user info for uid: ${uid}, readCount: ${readCount}, readDay: ${readDay}, favoriteCount: ${favoriteCount}`);

            // 6. 构建UserVO对象
            const userVO: UserVO = {
                id: userInfo.id,
                name: userInfo.name,
                avatar: avatar,
                readCount: readCount,
                readDay: readDay,
                favoriteCount: favoriteCount
            };

            return userVO;
        } catch (error) {
            console.error('Get user info error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`获取用户信息失败: ${errorMessage}`);
        }
    }

  
    /**
     * 更新用户头像
     * @param ctx Context对象
     * @param userId 用户ID
     * @param avatarUrl 头像URL
     * @returns 更新后的用户信息
     */
    async updateAvatar(ctx: Context<{ Bindings: Env }>, userId: string, avatarUrl: string) {
        const db = drizzle(ctx.env.DB);

        try {
            // 验证用户是否存在
            const existingUser = await db.select().from(user).where(eq(user.id, userId)).get();
            if (!existingUser) {
                throw new Error('用户不存在');
            }

            // 更新用户头像
            await db.update(user)
                .set({
                    avatar: avatarUrl,
                    updatedAt: new Date()
                })
                .where(eq(user.id, userId));

            console.log(`Updated avatar for user ${userId}: ${avatarUrl}`);

            // 返回更新后的用户信息
            return await this.getUserInfo(ctx, userId);
        } catch (error) {
            console.error('Update avatar error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`更新头像失败: ${errorMessage}`);
        }
    }
}
