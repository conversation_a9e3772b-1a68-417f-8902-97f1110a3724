import { Context } from "hono";
import { drizzle } from 'drizzle-orm/d1';
import { eq, and, sql } from 'drizzle-orm';
import { book } from "../db/schema/book";
import { category } from "../db/schema/category";
import { rating } from "../db/schema/rating";
import { history } from "../db/schema/history";
import { DateUtil } from "../utils/dateUtil";
import { nanoid } from "nanoid";

export class BookService {
    constructor() { }

    /// 上传书籍
    async uploadBook(ctx: Context<{ Bindings: Env }>, formData: FormData) {
        const db = drizzle(ctx.env.DB);

        const pdfFile = formData.get('pdf');
        const coverFile = formData.get('cover');
        const title = formData.get('title') as string;
        const categoryId = formData.get('categoryId') as string;
        const isFree = formData.get('isFree') === 'true';

        if (!pdfFile || !coverFile || !title || !categoryId) {
            throw new Error('Missing required fields');
        }

        // 检查文件类型
        if (!(pdfFile instanceof File) || !(coverFile instanceof File)) {
            throw new Error('Invalid file format');
        }

        const bookId = nanoid();
        const pdfFileName = `${bookId}-${pdfFile.name}`;
        const coverFileName = `${bookId}-${coverFile.name}`;

        // Upload to R2 - 使用正确的方法获取文件数据
        const coverBuffer = await coverFile.arrayBuffer();
        const pdfBuffer = await pdfFile.arrayBuffer();

        try {
            // 检查 R2 bucket 是否存在
            console.log('R2 bucket binding:', typeof ctx.env.KR_PUBLIC_BUCKET);
            console.log('R2 bucket methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(ctx.env.KR_PUBLIC_BUCKET)));

            // 添加适当的 headers 和 metadata
            const uploadOptions = {
                httpMetadata: {
                    contentType: coverFile.type || 'image/jpeg',
                },
            };

            const pdfUploadOptions = {
                httpMetadata: {
                    contentType: pdfFile.type || 'application/pdf',
                },
            };

            console.log('Uploading cover file:', coverFileName, 'size:', coverBuffer.byteLength);
            console.log('Uploading PDF file:', pdfFileName, 'size:', pdfBuffer.byteLength);
            console.log('Cover file type:', coverFile.type);
            console.log('PDF file type:', pdfFile.type);

            // 尝试上传文件
            const coverResult = await ctx.env.KR_PUBLIC_BUCKET.put(coverFileName, coverBuffer, uploadOptions);
            console.log('Cover upload result:', coverResult);

            const pdfResult = await ctx.env.KR_PUBLIC_BUCKET.put(pdfFileName, pdfBuffer, pdfUploadOptions);
            console.log('PDF upload result:', pdfResult);

            console.log('Files uploaded successfully to R2');
        } catch (error) {
            console.error('R2 upload error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to upload files to R2: ${errorMessage}`);
        }

        // Save to D1
        try {
            // 构建 R2 的网络 URL
            const coverUrl = coverFileName ? `${ctx.env.R2_PUBLIC_DOMAIN}/${coverFileName}` : null;
            const pdfUrl = `${ctx.env.R2_PUBLIC_DOMAIN}/${pdfFileName}`;
            
            console.log('Attempting to insert book with data:', {
                id: bookId,
                title,
                categoryId,
                isFree: isFree ? 1 : 0,
                url: pdfUrl,      // 使用 R2 URL
                cover: coverUrl,  // 使用 R2 URL
                level: 1,
            });
            
            const newBook = await db.insert(book).values({
                id: bookId,
                title,
                categoryId,
                isFree: isFree ? 1 : 0,
                url: pdfUrl,      // 使用 R2 URL
                cover: coverUrl,  // 使用 R2 URL
                level: 1,
            }).returning();
            
            console.log('Book inserted successfully:', newBook);
            return newBook;
        } catch (error) {
            console.error('Database insert error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : 'No stack trace';
            
            console.error('Error details:', {
                message: errorMessage,
                stack: errorStack
            });
            throw new Error(`Database insert failed: ${errorMessage}`);
        }
    }

    /// 获取书籍的url
    async getBookUrl(ctx: Context<{ Bindings: Env }>, bookId: string) {
        const db = drizzle(ctx.env.DB);
        const bookRecord = await db.select().from(book).where(eq(book.id, bookId)).get();
        if (!bookRecord) {
            throw new Error('Book not found');
        }
        // 直接返回数据库中存储的 URL
        return bookRecord.url;
    }

    /// 分页获取数据列表
    async getBookList(ctx: Context<{ Bindings: Env }>, page: number, pageSize: number) {
        const db = drizzle(ctx.env.DB);
        const books = await db.select().from(book).limit(pageSize).offset((page - 1) * pageSize);
        
        // 格式化日期字段
        const formattedBooks = books.map(book => ({
            ...book,
            createTime: DateUtil.formatToCustomDateTime(book.createTime),
            updateTime: DateUtil.formatToCustomDateTime(book.updateTime)
        }));
        
        return formattedBooks;
    }

    /// 根据分类获取书籍列表
    async getBooksByCategory(ctx: Context<{ Bindings: Env }>, categoryId: string, pageNo: number = 1, pageSize: number = 20) {
        const db = drizzle(ctx.env.DB);
        
        // 验证分页参数
        if (pageNo < 1) {
            throw new Error('页码必须大于等于1');
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new Error('每页大小必须在1-100之间');
        }
        
        try {
            // 首先检查分类是否存在
            const existingCategory = await db.select().from(category).where(eq(category.id, categoryId)).get();
            if (!existingCategory) {
                throw new Error('分类不存在');
            }
            
            // 计算偏移量
            const offset = (pageNo - 1) * pageSize;
            
            // 查询总数
            const totalResult = await db
                .select({ count: sql<number>`count(*)` })
                .from(book)
                .where(eq(book.categoryId, categoryId));
            
            const total = totalResult[0]?.count || 0;
            
            // 查询该分类下的书籍列表（带分页）
            const books = await db
                .select({
                    id: book.id,
                    title: book.title,
                    cover: book.cover,
                    url: book.url,
                    level: book.level,
                    categoryId: book.categoryId,
                    readCount: book.readCount,
                    favoriteCount: book.favoriteCount,
                    createTime: book.createTime,
                    updateTime: book.updateTime,
                    isFree: book.isFree
                })
                .from(book)
                .where(eq(book.categoryId, categoryId))
                .orderBy(book.createTime)
                .limit(pageSize)
                .offset(offset);
            
            console.log(`Getting books for category ${categoryId}, page ${pageNo}, size ${pageSize}, found ${books.length} books`);
            
            // 格式化书籍列表中的日期
            const formattedBooks = books.map(book => ({
                ...book,
                createTime: DateUtil.formatToCustomDateTime(book.createTime),
                updateTime: DateUtil.formatToCustomDateTime(book.updateTime)
            }));
            
            return {
                total,
                pageNo,
                pageSize,
                totalPages: Math.ceil(total / pageSize),
                hasNext: pageNo < Math.ceil(total / pageSize),
                hasPrev: pageNo > 1,
                category: {
                    id: existingCategory.id,
                    categoryName: existingCategory.categoryName,
                    cover: existingCategory.cover,
                    createTime: DateUtil.formatToCustomDateTime(existingCategory.createTime),
                    updateTime: DateUtil.formatToCustomDateTime(existingCategory.updateTime)
                },
                books: formattedBooks
            };
        } catch (error) {
            console.error('Get books by category error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`获取分类书籍列表失败: ${errorMessage}`);
        }
    }

    /// 编辑书籍
    async updateBook(ctx: Context<{ Bindings: Env }>, bookId: string, formData: FormData) {
        const db = drizzle(ctx.env.DB);

        // 检查书籍是否存在
        const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
        if (!existingBook) {
            throw new Error('书籍不存在');
        }

        const title = formData.get('title') as string;
        const categoryId = formData.get('categoryId') as string;
        const isFree = formData.get('isFree') === 'true';
        const level = parseInt(formData.get('level') as string) || existingBook.level;
        const pdfFile = formData.get('pdf') as File;
        const coverFile = formData.get('cover') as File;

        if (!title) {
            throw new Error('书籍标题不能为空');
        }

        let pdfUrl = existingBook.url;
        let coverUrl = existingBook.cover;

        // 如果有新的PDF文件，上传到 R2
        if (pdfFile && pdfFile instanceof File) {
            const pdfFileName = `${bookId}-${pdfFile.name}`;
            const pdfBuffer = await pdfFile.arrayBuffer();
            
            try {
                const pdfUploadOptions = {
                    httpMetadata: {
                        contentType: pdfFile.type || 'application/pdf',
                    },
                };
                
                console.log('Uploading new PDF file:', pdfFileName, 'size:', pdfBuffer.byteLength);
                await ctx.env.KR_PUBLIC_BUCKET.put(pdfFileName, pdfBuffer, pdfUploadOptions);
                console.log('New PDF file uploaded successfully');
                
                pdfUrl = `${ctx.env.R2_PUBLIC_DOMAIN}/${pdfFileName}`;
            } catch (error) {
                console.error('PDF upload error:', error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                throw new Error(`Failed to upload PDF file: ${errorMessage}`);
            }
        }

        // 如果有新的封面文件，上传到 R2
        if (coverFile && coverFile instanceof File) {
            const coverFileName = `${bookId}-${coverFile.name}`;
            const coverBuffer = await coverFile.arrayBuffer();
            
            try {
                const uploadOptions = {
                    httpMetadata: {
                        contentType: coverFile.type || 'image/jpeg',
                    },
                };
                
                console.log('Uploading new cover file:', coverFileName, 'size:', coverBuffer.byteLength);
                await ctx.env.KR_PUBLIC_BUCKET.put(coverFileName, coverBuffer, uploadOptions);
                console.log('New cover file uploaded successfully');
                
                coverUrl = `${ctx.env.R2_PUBLIC_DOMAIN}/${coverFileName}`;
            } catch (error) {
                console.error('Cover upload error:', error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                throw new Error(`Failed to upload cover file: ${errorMessage}`);
            }
        }

        // 更新数据库
        try {
            console.log('Updating book with data:', {
                id: bookId,
                title,
                categoryId,
                isFree: isFree ? 1 : 0,
                level,
                url: pdfUrl,
                cover: coverUrl,
            });

            const updatedBook = await db.update(book)
                .set({
                    title,
                    categoryId,
                    isFree: isFree ? 1 : 0,
                    level,
                    url: pdfUrl,
                    cover: coverUrl,
                })
                .where(eq(book.id, bookId))
                .returning();

            console.log('Book updated successfully:', updatedBook);
            return updatedBook;
        } catch (error) {
            console.error('Database update error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database update failed: ${errorMessage}`);
        }
    }

    /// 删除书籍
    async deleteBook(ctx: Context<{ Bindings: Env }>, bookId: string) {
        const db = drizzle(ctx.env.DB);

        // 检查书籍是否存在
        const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
        if (!existingBook) {
            throw new Error('书籍不存在');
        }

        try {
            // 从 R2 删除文件
            const filesToDelete = [];
            
            // 提取文件名（从 URL 中提取）
            if (existingBook.url) {
                const urlParts = existingBook.url.split('/');
                const pdfFileName = urlParts[urlParts.length - 1];
                filesToDelete.push(pdfFileName);
                console.log('Will delete PDF file:', pdfFileName);
            }
            
            if (existingBook.cover) {
                const urlParts = existingBook.cover.split('/');
                const coverFileName = urlParts[urlParts.length - 1];
                filesToDelete.push(coverFileName);
                console.log('Will delete cover file:', coverFileName);
            }

            // 批量删除 R2 文件
            if (filesToDelete.length > 0) {
                try {
                    await ctx.env.KR_PUBLIC_BUCKET.delete(filesToDelete);
                    console.log('Files deleted from R2 successfully:', filesToDelete);
                } catch (error) {
                    console.error('R2 file deletion error:', error);
                    // 即使文件删除失败，也继续删除数据库记录
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    console.warn(`Failed to delete files from R2: ${errorMessage}`);
                }
            }

            // 从数据库删除记录
            await db.delete(book).where(eq(book.id, bookId));
            console.log('Book deleted from database successfully:', bookId);
            
            return { 
                success: true, 
                message: '书籍删除成功',
                deletedFiles: filesToDelete
            };
        } catch (error) {
            console.error('Database delete error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database delete failed: ${errorMessage}`);
        }
    }

    /// 用户为书籍评分
    async rateBook(ctx: Context<{ Bindings: Env }>, bookId: string, score: number) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证评分范围
        if (score < 1 || score > 5) {
            throw new Error('评分必须在1-5之间');
        }
        
        // 检查书籍是否存在
        const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
        if (!existingBook) {
            throw new Error('书籍不存在');
        }
        
        try {
            // 检查用户是否已经评分过这本书
            const existingRating = await db.select().from(rating)
                .where(and(eq(rating.userId, userId), eq(rating.bookId, bookId)))
                .get();
            
            if (existingRating) {
                // 更新现有评分
                console.log(`Updating existing rating for user ${userId}, book ${bookId}, score ${score}`);
                const updatedRating = await db.update(rating)
                    .set({
                        score,
                        updateTime: new Date()
                    })
                    .where(and(eq(rating.userId, userId), eq(rating.bookId, bookId)))
                    .returning();
                
                console.log('Rating updated successfully:', updatedRating);
                return updatedRating;
            } else {
                // 创建新评分
                const ratingId = nanoid();
                console.log(`Creating new rating for user ${userId}, book ${bookId}, score ${score}`);
                const newRating = await db.insert(rating).values({
                    id: ratingId,
                    userId,
                    bookId,
                    score,
                    createTime: new Date(),
                    updateTime: new Date()
                }).returning();
                
                console.log('Rating created successfully:', newRating);
                return newRating;
            }
        } catch (error) {
            console.error('Rating operation error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Rating operation failed: ${errorMessage}`);
        }
    }

    /// 更新用户阅读进度
    async updateReadProgress(ctx: Context<{ Bindings: Env }>, bookId: string, progress: number) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证进度是否为正整数
        if (!Number.isInteger(progress) || progress < 0) {
            throw new Error('进度必须大于等于0');
        }
        
        // 检查书籍是否存在
        const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
        if (!existingBook) {
            throw new Error('书籍不存在');
        }
        
        try {
            // 检查用户是否已经有阅读记录
            const existingHistory = await db.select().from(history)
                .where(and(eq(history.userId, userId), eq(history.bookId, bookId)))
                .get();
            
            if (existingHistory) {
                // 更新现有阅读记录
                console.log(`Updating existing read progress for user ${userId}, book ${bookId}, progress ${progress}`);
                const updatedHistory = await db.update(history)
                    .set({
                        progress,
                        lastViewedTime: new Date(),
                        updateTime: new Date()
                    })
                    .where(and(eq(history.userId, userId), eq(history.bookId, bookId)))
                    .returning();
                
                console.log('Read progress updated successfully:', updatedHistory);
                return updatedHistory;
            } else {
                // 创建新阅读记录
                const historyId = nanoid();
                console.log(`Creating new read progress for user ${userId}, book ${bookId}, progress ${progress}`);
                const newHistory = await db.insert(history).values({
                    id: historyId,
                    userId,
                    bookId,
                    progress,
                    lastViewedTime: new Date(),
                    createTime: new Date(),
                    updateTime: new Date()
                }).returning();
                
                console.log('Read progress created successfully:', newHistory);
                return newHistory;
            }
        } catch (error) {
            console.error('Read progress operation error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Read progress operation failed: ${errorMessage}`);
        }
    }

    /// 获取书籍详情
    async getBookDetail(ctx: Context<{ Bindings: Env }>, bookId: string) {
        const db = drizzle(ctx.env.DB);
        
        // 验证参数
        if (!bookId) {
            throw new Error('书籍ID不能为空');
        }
        
        try {
            // 检查书籍是否存在
            const existingBook = await db.select().from(book).where(eq(book.id, bookId)).get();
            if (!existingBook) {
                throw new Error('书籍不存在');
            }
            
            // 从Context中获取用户ID
            const userId = ctx.get('userId');
            let progress = 0;
            
            // 如果用户已登录，获取阅读进度
            if (userId) {
                const userHistory = await db.select().from(history)
                    .where(and(eq(history.userId, userId), eq(history.bookId, bookId)))
                    .get();
                
                if (userHistory) {
                    progress = userHistory.progress || 0;
                }
            }
            
            // 组装返回数据
            const result = {
                bookId: existingBook.id,
                bookName: existingBook.title,
                bookUrl: existingBook.url,
                progress: progress,
                quizStatus: 0 // 暂时固定为0
            };
            
            console.log(`Getting book detail for book ${bookId}, user ${userId}, progress ${progress}`);
            return result;
        } catch (error) {
            console.error('Get book detail error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`获取书籍详情失败: ${errorMessage}`);
        }
    }
}
