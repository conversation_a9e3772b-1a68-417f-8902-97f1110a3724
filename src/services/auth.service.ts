import { drizzle } from 'drizzle-orm/d1'
import { user } from '../db/schema/user'
import { eq } from 'drizzle-orm'
import type { Context } from 'hono'
import { nanoid } from 'nanoid';
import { generateToken } from '../utils/token';
import { AppleAuthUtil } from '../utils/appleAuth';
import { NicknameService } from './nickname.service';
import { DateUtil } from '../utils/dateUtil';
import type { AppleAuthDTO, AuthVO, UserCreateInput } from '../types/auth.types';

export class AuthService {
    private nicknameService = new NicknameService();

    foo() {
        return 'foo';
    }
    async login(ctx: Context<{ Bindings: Env }>, appleAuthId: string): Promise<string> {
        try {
            console.log('AuthService appleAuthId', appleAuthId);
            const db = drizzle(ctx.env.DB);
            // 1. 检查用户是否存在
            const existingUser = await db.select()
                .from(user)
                .where(eq(user.appleAuthId, appleAuthId))
                .get();
            console.log('existingUser', existingUser);
            const now = new Date();
            if (!existingUser) {
                // 2. 如果用户不存在，则创建用户
                const userId = nanoid();
                const token = await generateToken(userId);
                const newUser = await db.insert(user).values({
                    id: userId,
                    appleAuthId,
                    name: '',
                    avatar: '',
                    token,
                    createdAt: now,
                    updatedAt: now,
                }).returning().get();
                return newUser.token;
            }
            // 3. 如果用户存在，则更新用户 token 和 updatedAt
            // 生成新的token
            const token = await generateToken(existingUser.id);
            const updatedUser = await db.update(user).set({
                token,
                updatedAt: now,
            }).where(eq(user.id, existingUser.id)).returning().get();
            return updatedUser.token;
        } catch (error) {
            console.error('Error in login:', error);
            throw error;
        }
    }

    /**
     * Apple 登录方法
     * 完全复刻 Java 项目中的 loginWithApple 方法逻辑
     * @param ctx Hono Context
     * @param appleAuthDTO Apple 登录 DTO
     * @returns AuthVO 包含用户ID、token和是否新用户标识
     */
    async loginWithApple(ctx: Context<{ Bindings: Env }>, appleAuthDTO: AppleAuthDTO): Promise<AuthVO> {
        try {
            console.log('AuthService loginWithApple called with:', {
                hasIdentityToken: !!appleAuthDTO.identityToken,
                givenName: appleAuthDTO.givenName,
                familyName: appleAuthDTO.familyName,
                hasEmail: !!appleAuthDTO.email
            });

            // 1. 获取环境配置 - 对应 Java 中的 appleAuthConfig
            const appleIssuer = ctx.env.APPLE_ISSUER;
            const clientId = ctx.env.APPLE_CLIENT_ID;
            const defaultAvatar = ctx.env.DEFAULT_AVATAR;
            
            if (!clientId) {
                throw new Error('Apple Client ID not configured in environment variables');
            }

            // 2. 解析 Apple Identity Token - 对应 Java 中的 DecodedJWT jwt = JWT.decode(identityToken)
            const appleAuthUtil = new AppleAuthUtil(appleIssuer, clientId);
            const applePayload = appleAuthUtil.decodeAppleToken(appleAuthDTO.identityToken);
            
            // 3. 验证 Apple Token - 对应 Java 中的 issuer/audience 验证逻辑
            appleAuthUtil.validateAppleToken(applePayload);

            // 4. 获取 Apple 用户 ID - 对应 Java 中的 final String appleUserId = jwt.getSubject()
            const appleUserId = applePayload.sub;
            console.log('Apple user ID extracted:', appleUserId);

            // 5. 查询数据库是否有该用户 - 对应 Java 中的 lambdaQuery().eq(User::getAppleUserId, appleUserId).one()
            const db = drizzle(ctx.env.DB);
            const existingUser = await db.select()
                .from(user)
                .where(eq(user.appleAuthId, appleUserId))
                .get();

            console.log('Existing user found:', !!existingUser);

            if (existingUser) {
                // 6a. 有用户：更新用户token - 对应 Java 中的 updateUserByLogin(user)
                return await this.updateUserByLogin(ctx, existingUser);
            } else {
                // 6b. 没有用户：创建新用户 - 对应 Java 中的 createNewUserByAppleId
                const newUserData = await this.createNewUserByAppleId(ctx, appleUserId, appleAuthDTO, defaultAvatar);
                return {
                    userId: newUserData.id,
                    token: newUserData.token,
                    isNew: 1 // 对应 Java 中的 new AuthVO(user.getId(), user.getToken(), 1)
                };
            }
        } catch (error) {
            console.error('Apple login failed:', error);
            // 对应 Java 中的 throw new IllegalArgumentException("Apple 登录失败")
            throw new Error(`Apple 登录失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * 更新已存在用户的登录信息
     * 对应 Java 项目中的 updateUserByLogin 方法
     */
    private async updateUserByLogin(ctx: Context<{ Bindings: Env }>, existingUser: any): Promise<AuthVO> {
        const db = drizzle(ctx.env.DB);
        const now = new Date();
        
        // 生成新的 token - 对应 Java 中的 refreshUserToken(user.getId())
        const newToken = await generateToken(existingUser.id);
        
        // 更新用户 token 和更新时间
        await db.update(user)
            .set({
                token: newToken,
                updatedAt: now,
            })
            .where(eq(user.id, existingUser.id))
            .run();

        console.log('Updated existing user token for user:', existingUser.id);
        
        // 对应 Java 中的 return new AuthVO(user.getId(), token, 0)
        return {
            userId: existingUser.id,
            token: newToken,
            isNew: 0
        };
    }

    /**
     * 通过 Apple ID 创建新用户
     * 对应 Java 项目中的 createNewUserByAppleId 方法
     */
    private async createNewUserByAppleId(
        ctx: Context<{ Bindings: Env }>, 
        appleUserId: string, 
        appleAuthDTO: AppleAuthDTO,
        defaultAvatar: string
    ): Promise<UserCreateInput> {
        const db = drizzle(ctx.env.DB);
        const userId = nanoid();
        const token = await generateToken(userId);
        const now = new Date();
        
        // 生成用户名 - 对应 Java 中的用户名生成逻辑
        const userName = this.nicknameService.generateFromAppleInfo(
            appleAuthDTO.givenName,
            appleAuthDTO.familyName
        );
        
        console.log('Creating new user with Apple ID:', {
            userId,
            appleUserId,
            userName,
            hasEmail: !!appleAuthDTO.email
        });

        // 创建用户记录 - 对应 Java 中的 save(user)
        const userData: UserCreateInput = {
            id: userId,
            appleAuthId: appleUserId,
            name: userName,
            avatar: defaultAvatar,
            token
        };

        await db.insert(user).values({
            id: userId,
            appleAuthId: appleUserId,
            name: userName,
            avatar: defaultAvatar,
            token,
            createdAt: now,
            updatedAt: now,
        }).run();

        console.log('New user created successfully:', userId);
        return userData;
    }

    logout(userId: string) {

    }

    /// 获取用户信息
    async getUserInfo(ctx: Context<{ Bindings: Env }>) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        try {
            // 从数据库获取用户信息
            const userInfo = await db.select({
                id: user.id,
                name: user.name,
                avatar: user.avatar,
                appleAuthId: user.appleAuthId,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            })
            .from(user)
            .where(eq(user.id, userId))
            .get();
            
            if (!userInfo) {
                throw new Error('用户不存在');
            }
            
            // 格式化日期字段
            const formattedUserInfo = {
                ...userInfo,
                createdAt: DateUtil.formatToCustomDateTime(userInfo.createdAt),
                updatedAt: DateUtil.formatToCustomDateTime(userInfo.updatedAt)
            };
            
            console.log('User info retrieved successfully:', formattedUserInfo);
            return formattedUserInfo;
        } catch (error) {
            console.error('Get user info error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`获取用户信息失败: ${errorMessage}`);
        }
    }

    /// 更新用户信息
    async updateUserInfo(ctx: Context<{ Bindings: Env }>, name?: string, avatar?: string) {
        const db = drizzle(ctx.env.DB);
        
        // 从Context中获取用户ID
        const userId = ctx.get('userId');
        if (!userId) {
            throw new Error('用户未登录');
        }
        
        // 验证参数
        if (name !== undefined && (typeof name !== 'string' || name.trim() === '')) {
            throw new Error('姓名不能为空');
        }
        
        if (avatar !== undefined && (typeof avatar !== 'string' || avatar.trim() === '')) {
            throw new Error('头像URL不能为空');
        }
        
        try {
            // 检查用户是否存在
            const existingUser = await db.select()
                .from(user)
                .where(eq(user.id, userId))
                .get();
                
            if (!existingUser) {
                throw new Error('用户不存在');
            }
            
            // 构建更新数据
            const updateData: any = {
                updatedAt: new Date()
            };
            
            if (name !== undefined) {
                updateData.name = name.trim();
            }
            
            if (avatar !== undefined) {
                updateData.avatar = avatar.trim();
            }
            
            // 更新用户信息
            console.log(`Updating user ${userId} with data:`, updateData);
            const updatedUser = await db.update(user)
                .set(updateData)
                .where(eq(user.id, userId))
                .returning({
                    id: user.id,
                    name: user.name,
                    avatar: user.avatar,
                    appleAuthId: user.appleAuthId,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt
                })
                .get();
            
            // 格式化日期字段
            const formattedUser = {
                ...updatedUser,
                createdAt: DateUtil.formatToCustomDateTime(updatedUser.createdAt),
                updatedAt: DateUtil.formatToCustomDateTime(updatedUser.updatedAt)
            };
            
            console.log('User updated successfully:', formattedUser);
            return formattedUser;
        } catch (error) {
            console.error('Update user info error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`更新用户信息失败: ${errorMessage}`);
        }
    }
}