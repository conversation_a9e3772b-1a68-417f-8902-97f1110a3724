/**
 * 昵称生成服务
 * 复刻 Java 项目中的 NicknameGeneratorService 和用户名生成逻辑
 */
export class NicknameService {
  // 形容词词库 - 用于生成默认昵称
  private readonly adjectives = [
    'Cool', 'Smart', 'Happy', '<PERSON>', '<PERSON>', '<PERSON>', 'Nice', 'Kind',
    'Super', 'Amazing', 'Awesome', 'Great', 'Fantastic', 'Wonderful',
    'Clev<PERSON>', 'Brave', 'Strong', 'Gentle', 'Creative', 'Funny'
  ];

  // 名词词库 - 用于生成默认昵称
  private readonly nouns = [
    'User', 'Player', 'Friend', 'Star', 'Hero', 'Champion', 'Explorer',
    'Creator', 'Builder', 'Reader', 'Learner', 'Dreamer', 'Thinker',
    'Writer', 'Artist', 'Coder', 'Gamer', 'Lover', 'Seeker', 'Maker'
  ];

  /**
   * 从 Apple 用户信息生成昵称
   * 复刻 Java 项目中 createNewUserByAppleId 方法的用户名生成逻辑：
   * if (StrUtil.isBlankIfStr(givenName) && StrUtil.isBlankIfStr(familyName)) {
   *     userName = nicknameGeneratorService.generateDefaultNickname();
   * } else {
   *     userName = givenName + "\t" + familyName;
   * }
   * 
   * @param givenName Apple 提供的名
   * @param familyName Apple 提供的姓
   * @returns 生成的用户名
   */
  generateFromAppleInfo(givenName?: string, familyName?: string): string {
    // 检查是否有有效的姓名信息
    const hasGivenName = givenName && givenName.trim() !== '';
    const hasFamilyName = familyName && familyName.trim() !== '';

    if (hasGivenName || hasFamilyName) {
      // 如果有姓名信息，使用 Java 项目中的格式：givenName + "\t" + familyName
      const names = [];
      if (hasGivenName) names.push(givenName.trim());
      if (hasFamilyName) names.push(familyName.trim());
      
      // 使用 \t 分隔符，与 Java 项目保持一致
      return names.join('\t');
    } else {
      // 如果没有姓名信息，生成默认昵称
      return this.generateDefaultNickname();
    }
  }

  /**
   * 生成默认昵称
   * 复刻 Java 项目中的 nicknameGeneratorService.generateDefaultNickname() 逻辑
   * 格式：形容词 + 名词 + 随机数字
   * @returns 生成的默认昵称
   */
  generateDefaultNickname(): string {
    const randomAdjective = this.adjectives[Math.floor(Math.random() * this.adjectives.length)];
    const randomNoun = this.nouns[Math.floor(Math.random() * this.nouns.length)];
    const randomNumber = Math.floor(Math.random() * 1000);
    
    return `${randomAdjective}${randomNoun}${randomNumber}`;
  }

  /**
   * 验证用户名是否有效
   * @param name 用户名
   * @returns 是否有效
   */
  validateNickname(name: string): boolean {
    if (!name || name.trim() === '') {
      return false;
    }
    
    // 长度检查（假设最大100字符，与数据库schema一致）
    if (name.length > 100) {
      return false;
    }
    
    return true;
  }
}
