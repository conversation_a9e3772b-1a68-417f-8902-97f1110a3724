import { Context } from "hono";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export class R2Service {
    /**
     * 生成安全的文件key
     */
    private generateSecureKey(userId: string, folder: string, fileName: string): string {
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substring(2, 15);
        const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'bin';
        
        // 清理文件名，移除特殊字符
        const cleanFolder = folder.replace(/[^a-zA-Z0-9\-_\/]/g, '');
        
        return `${cleanFolder}/${userId}/${timestamp}_${randomId}.${fileExtension}`;
    }

    /**
     * 获取 presigned URL
     * @param ctx Context对象
     * @param userId 用户ID
     * @param folder 文件夹路径
     * @param fileName 文件名
     * @param contentType 文件类型
     * @returns presigned URL 和公开访问 URL
     */
    async getPresignedURL(
        ctx: Context<{ Bindings: Env }>,
        userId: string,
        folder: string,
        fileName: string,
        contentType: string
    ) {
        try {
            // 生成安全的文件key
            const key = this.generateSecureKey(userId, folder, fileName);

            console.log(`生成 presigned URL: userId=${userId}, folder=${folder}, fileName=${fileName}`);

            const s3Client = new S3Client({
                region: 'auto',
                endpoint: ctx.env.ENDPOINT,
                credentials: {
                    accessKeyId: ctx.env.R2_ACCESS_KEY_ID,
                    secretAccessKey: ctx.env.R2_SECRET_ACCESS_KEY,
                },
            });

            const command = new PutObjectCommand({
                Bucket: ctx.env.R2_BUCKET_NAME,
                Key: key,
                ContentType: contentType,
            });

            const presignedUrl = await getSignedUrl(s3Client, command, { 
                expiresIn: 3600 // 1小时过期
            });

            // 生成公开访问的 URL
            const publicUrl = `https://${ctx.env.R2_PUBLIC_DOMAIN}/${key}`;

            console.log(`成功生成 presigned URL: key=${key}`);

            return {
                uploadUrl: presignedUrl,
                publicUrl: publicUrl,
                key: key,
                expiresIn: 3600,
                contentType: contentType
            };
        } catch (error) {
            console.error('生成 presigned URL 失败:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`生成上传链接失败: ${errorMessage}`);
        }
    }
}
