import { Context } from "hono";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export class R2Service {
    // 允许的文件类型配置
    private static readonly ALLOWED_MIME_TYPES = {
        // 图片类型
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
        'image/webp': ['.webp'],
        'image/svg+xml': ['.svg'],
        // 文档类型
        'application/pdf': ['.pdf'],
        'text/plain': ['.txt'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        // 视频类型
        'video/mp4': ['.mp4'],
        'video/mpeg': ['.mpeg', '.mpg'],
        'video/quicktime': ['.mov'],
        // 音频类型
        'audio/mpeg': ['.mp3'],
        'audio/wav': ['.wav'],
        'audio/ogg': ['.ogg']
    };

    // 文件大小限制 (字节)
    private static readonly MAX_FILE_SIZE = {
        'image': 10 * 1024 * 1024,    // 10MB for images
        'video': 100 * 1024 * 1024,   // 100MB for videos
        'audio': 50 * 1024 * 1024,    // 50MB for audio
        'document': 20 * 1024 * 1024, // 20MB for documents
        'default': 10 * 1024 * 1024   // 10MB default
    };

    /**
     * 验证文件类型是否允许
     */
    private validateFileType(contentType: string, fileName: string): void {
        const allowedTypes = R2Service.ALLOWED_MIME_TYPES as Record<string, string[]>;
        if (!allowedTypes[contentType]) {
            throw new Error(`不支持的文件类型: ${contentType}`);
        }

        const fileExtension = '.' + fileName.split('.').pop()?.toLowerCase();
        const allowedExtensions = allowedTypes[contentType];
        
        if (!allowedExtensions.includes(fileExtension)) {
            throw new Error(`文件扩展名 ${fileExtension} 与 MIME 类型 ${contentType} 不匹配`);
        }
    }

    /**
     * 获取文件类型分类
     */
    private getFileCategory(contentType: string): string {
        if (contentType.startsWith('image/')) return 'image';
        if (contentType.startsWith('video/')) return 'video';
        if (contentType.startsWith('audio/')) return 'audio';
        if (contentType.includes('pdf') || contentType.includes('document') || contentType.includes('text')) return 'document';
        return 'default';
    }

    /**
     * 获取文件大小限制
     */
    private getMaxFileSize(contentType: string): number {
        const category = this.getFileCategory(contentType);
        const sizeConfig = R2Service.MAX_FILE_SIZE as Record<string, number>;
        return sizeConfig[category] || sizeConfig.default;
    }

    /**
     * 生成安全的文件key
     */
    private generateSecureKey(basePath: string, fileName: string): string {
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substring(2, 15);
        const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'bin';
        
        // 清理文件名，移除特殊字符
        const cleanBasePath = basePath.replace(/[^a-zA-Z0-9\-_\/]/g, '');
        
        return `${cleanBasePath}/${timestamp}_${randomId}.${fileExtension}`;
    }
    /**
     * 生成上传 presigned URL
     * @param ctx Context对象
     * @param key 文件路径
     * @param contentType 文件类型
     * @param expiresIn 过期时间（秒）
     * @param fileName 文件名（用于验证）
     * @param maxFileSize 最大文件大小（可选，会自动根据文件类型确定）
     * @returns presigned URL 和公开访问 URL
     */
    async generateUploadUrl(
        ctx: Context<{ Bindings: Env }>,
        key: string,
        contentType: string,
        expiresIn: number = 3600,
        fileName?: string,
        maxFileSize?: number
    ) {
        try {
            // 如果提供了文件名，进行文件类型验证
            if (fileName) {
                this.validateFileType(contentType, fileName);
            }

            // 确定文件大小限制
            const fileSizeLimit = maxFileSize || this.getMaxFileSize(contentType);

            console.log(`生成上传URL: key=${key}, contentType=${contentType}, maxSize=${fileSizeLimit}MB`);

            const s3Client = new S3Client({
                region: 'auto',
                endpoint: ctx.env.ENDPOINT,
                credentials: {
                    accessKeyId: ctx.env.R2_ACCESS_KEY_ID,
                    secretAccessKey: ctx.env.R2_SECRET_ACCESS_KEY,
                },
            });

            const command = new PutObjectCommand({
                Bucket: ctx.env.R2_BUCKET_NAME,
                Key: key,
                ContentType: contentType,
                // 添加元数据
                Metadata: {
                    'upload-timestamp': Date.now().toString(),
                    'max-file-size': fileSizeLimit.toString()
                }
            });

            const presignedUrl = await getSignedUrl(s3Client, command, { 
                expiresIn,
                // 添加条件检查，限制文件大小
                signableHeaders: new Set(['content-type']),
            });

            // 生成公开访问的 URL
            const publicUrl = `https://${ctx.env.R2_PUBLIC_DOMAIN}/${key}`;

            console.log(`成功生成上传URL: key=${key}, expires=${expiresIn}s`);

            return {
                uploadUrl: presignedUrl,
                publicUrl: publicUrl,
                key: key,
                expiresIn: expiresIn,
                maxFileSize: fileSizeLimit,
                contentType: contentType
            };
        } catch (error) {
            console.error('生成上传URL失败:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`生成上传链接失败: ${errorMessage}`);
        }
    }

    /**
     * 生成头像上传 URL
     * @param ctx Context对象
     * @param userId 用户ID
     * @param fileName 文件名
     * @param contentType 文件类型
     * @returns presigned URL 和公开访问 URL
     */
    async generateAvatarUploadUrl(
        ctx: Context<{ Bindings: Env }>,
        userId: string,
        fileName: string,
        contentType: string
    ) {
        try {
            // 验证头像文件类型（只允许图片）
            if (!contentType.startsWith('image/')) {
                throw new Error('头像只支持图片格式');
            }

            // 生成安全的头像文件key
            const key = this.generateSecureKey(`avatars/${userId}`, fileName);

            console.log(`生成头像上传URL: userId=${userId}, fileName=${fileName}`);

            return await this.generateUploadUrl(ctx, key, contentType, 3600, fileName);
        } catch (error) {
            console.error('生成头像上传URL失败:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`生成头像上传链接失败: ${errorMessage}`);
        }
    }

    /**
     * 生成通用文件上传 URL
     * @param ctx Context对象
     * @param folder 文件夹路径
     * @param fileName 文件名
     * @param contentType 文件类型
     * @returns presigned URL 和公开访问 URL
     */
    async generateFileUploadUrl(
        ctx: Context<{ Bindings: Env }>,
        folder: string,
        fileName: string,
        contentType: string
    ) {
        try {
            // 生成安全的文件key
            const key = this.generateSecureKey(folder, fileName);

            console.log(`生成文件上传URL: folder=${folder}, fileName=${fileName}`);

            return await this.generateUploadUrl(ctx, key, contentType, 3600, fileName);
        } catch (error) {
            console.error('生成文件上传URL失败:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`生成文件上传链接失败: ${errorMessage}`);
        }
    }
}