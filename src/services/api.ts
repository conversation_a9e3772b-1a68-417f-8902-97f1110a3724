import { http, ApiResponse } from '../utils/http';

// 用户相关接口
export interface User {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

// 文章相关接口
export interface Post {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  views: number;
}

// 用户 API 服务
export class UserApi {
  private baseUrl = '/api/users';

  // 获取用户列表
  async getUsers(): Promise<ApiResponse<User[]>> {
    return http.get<User[]>(this.baseUrl);
  }

  // 获取单个用户
  async getUser(id: number): Promise<ApiResponse<User>> {
    return http.get<User>(`${this.baseUrl}/${id}`);
  }

  // 创建用户
  async createUser(user: Omit<User, 'id'>): Promise<ApiResponse<User>> {
    return http.post<User>(this.baseUrl, user);
  }

  // 更新用户
  async updateUser(id: number, user: Partial<User>): Promise<ApiResponse<User>> {
    return http.put<User>(`${this.baseUrl}/${id}`, user);
  }

  // 删除用户
  async deleteUser(id: number): Promise<ApiResponse<void>> {
    return http.delete<void>(`${this.baseUrl}/${id}`);
  }
}

// 文章 API 服务
export class PostApi {
  private baseUrl = '/api/posts';

  // 获取文章列表
  async getPosts(): Promise<ApiResponse<Post[]>> {
    return http.get<Post[]>(this.baseUrl);
  }

  // 获取单个文章
  async getPost(id: number): Promise<ApiResponse<Post>> {
    return http.get<Post>(`${this.baseUrl}/${id}`);
  }

  // 创建文章
  async createPost(post: Omit<Post, 'id' | 'createdAt' | 'views'>): Promise<ApiResponse<Post>> {
    return http.post<Post>(this.baseUrl, post);
  }

  // 更新文章
  async updatePost(id: number, post: Partial<Post>): Promise<ApiResponse<Post>> {
    return http.put<Post>(`${this.baseUrl}/${id}`, post);
  }

  // 删除文章
  async deletePost(id: number): Promise<ApiResponse<void>> {
    return http.delete<void>(`${this.baseUrl}/${id}`);
  }

  // 增加文章浏览量
  async incrementViews(id: number): Promise<ApiResponse<void>> {
    return http.patch<void>(`${this.baseUrl}/${id}/views`);
  }
}

// 文件上传 API 服务
export class FileApi {
  private baseUrl = '/api/files';

  // 上传文件
  async uploadFile(formData: FormData): Promise<ApiResponse<{ url: string }>> {
    return http.upload<{ url: string }>(`${this.baseUrl}/upload`, formData);
  }

  // 下载文件
  async downloadFile(filename: string): Promise<Response> {
    return http.download(`${this.baseUrl}/download/${filename}`);
  }
}

// 创建服务实例
export const userApi = new UserApi();
export const postApi = new PostApi();
export const fileApi = new FileApi(); 