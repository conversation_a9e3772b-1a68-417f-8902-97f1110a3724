import { Context } from "hono";
import { drizzle } from 'drizzle-orm/d1';
import { eq } from 'drizzle-orm';
import { category } from "../db/schema/category";
import { nanoid } from "nanoid";

export class CategoryService {
    constructor() { }

    /// 新增分类
    async createCategory(ctx: Context<{ Bindings: Env }>, formData: FormData) {
        const db = drizzle(ctx.env.DB);

        const categoryName = formData.get('categoryName') as string;
        const coverFile = formData.get('cover') as File;

        if (!categoryName) {
            throw new Error('分类名称不能为空');
        }

        const categoryId = nanoid();
        let coverUrl = null;

        // 如果有封面文件，上传到 R2
        if (coverFile && coverFile instanceof File) {
            const coverFileName = `${categoryId}-${coverFile.name}`;
            const coverBuffer = await coverFile.arrayBuffer();
            
            try {
                const uploadOptions = {
                    httpMetadata: {
                        contentType: coverFile.type || 'image/jpeg',
                    },
                };
                
                console.log('Uploading category cover:', coverFileName, 'size:', coverBuffer.byteLength);
                await ctx.env.KR_PUBLIC_BUCKET.put(coverFileName, coverBuffer, uploadOptions);
                console.log('Category cover uploaded successfully');
                
                // 构建 R2 的网络 URL
                coverUrl = `${ctx.env.R2_PUBLIC_DOMAIN}/${coverFileName}`;
            } catch (error) {
                console.error('Category cover upload error:', error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                throw new Error(`Failed to upload category cover: ${errorMessage}`);
            }
        }

        // 保存到数据库
        try {
            const newCategory = await db.insert(category).values({
                id: categoryId,
                categoryName,
                cover: coverUrl,  // 使用 R2 URL 而不是文件名
            }).returning();

            console.log('Category created successfully:', newCategory);
            return newCategory;
        } catch (error) {
            console.error('Database insert error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database insert failed: ${errorMessage}`);
        }
    }

    /// 修改分类
    async updateCategory(ctx: Context<{ Bindings: Env }>, categoryId: string, formData: FormData) {
        const db = drizzle(ctx.env.DB);

        const categoryName = formData.get('categoryName') as string;
        const coverFile = formData.get('cover') as File;

        if (!categoryName) {
            throw new Error('分类名称不能为空');
        }

        // 检查分类是否存在
        const existingCategory = await db.select().from(category).where(eq(category.id, categoryId)).get();
        if (!existingCategory) {
            throw new Error('分类不存在');
        }

        let coverUrl = existingCategory.cover;

        // 如果有新的封面文件，上传到 R2
        if (coverFile && coverFile instanceof File) {
            const coverFileName = `${categoryId}-${coverFile.name}`;
            const coverBuffer = await coverFile.arrayBuffer();
            
            try {
                const uploadOptions = {
                    httpMetadata: {
                        contentType: coverFile.type || 'image/jpeg',
                    },
                };
                
                console.log('Uploading new category cover:', coverFileName, 'size:', coverBuffer.byteLength);
                await ctx.env.KR_PUBLIC_BUCKET.put(coverFileName, coverBuffer, uploadOptions);
                console.log('New category cover uploaded successfully');
                
                // 构建 R2 的网络 URL
                coverUrl = `${ctx.env.R2_PUBLIC_DOMAIN}/${coverFileName}`;
            } catch (error) {
                console.error('Category cover upload error:', error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                throw new Error(`Failed to upload category cover: ${errorMessage}`);
            }
        }

        // 更新数据库
        try {
            const updatedCategory = await db.update(category)
                .set({
                    categoryName,
                    cover: coverUrl,  // 使用 R2 URL 而不是文件名
                })
                .where(eq(category.id, categoryId))
                .returning();

            console.log('Category updated successfully:', updatedCategory);
            return updatedCategory;
        } catch (error) {
            console.error('Database update error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database update failed: ${errorMessage}`);
        }
    }

    /// 删除分类
    async deleteCategory(ctx: Context<{ Bindings: Env }>, categoryId: string) {
        const db = drizzle(ctx.env.DB);

        // 检查分类是否存在
        const existingCategory = await db.select().from(category).where(eq(category.id, categoryId)).get();
        if (!existingCategory) {
            throw new Error('分类不存在');
        }

        // 检查是否有书籍使用此分类
        const { book } = await import('../db/schema/book');
        const booksWithCategory = await db.select().from(book).where(eq(book.categoryId, categoryId)).all();
        if (booksWithCategory.length > 0) {
            throw new Error('该分类下还有书籍，无法删除');
        }

        // 删除 R2 文件（如果存在）
        const deletedFiles: string[] = [];
        if (existingCategory.cover) {
            try {
                // 从 URL 中提取文件名
                const fileName = existingCategory.cover.split('/').pop();
                if (fileName) {
                    await ctx.env.KR_PUBLIC_BUCKET.delete(fileName);
                    deletedFiles.push(fileName);
                    console.log('Category cover file deleted:', fileName);
                }
            } catch (error) {
                console.error('R2 file deletion error:', error);
                // 即使文件删除失败，也继续删除数据库记录
            }
        }

        // 删除分类
        try {
            await db.delete(category).where(eq(category.id, categoryId));
            console.log('Category deleted successfully:', categoryId);
            return { 
                success: true, 
                message: '分类删除成功',
                deletedFiles
            };
        } catch (error) {
            console.error('Database delete error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database delete failed: ${errorMessage}`);
        }
    }

    /// 查询所有分类
    async getAllCategories(ctx: Context<{ Bindings: Env }>) {
        const db = drizzle(ctx.env.DB);

        try {
            const categories = await db.select().from(category).all();
            console.log('Categories retrieved successfully:', categories.length);
            return categories;
        } catch (error) {
            console.error('Database query error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database query failed: ${errorMessage}`);
        }
    }

    /// 根据ID查询分类
    async getCategoryById(ctx: Context<{ Bindings: Env }>, categoryId: string) {
        const db = drizzle(ctx.env.DB);

        try {
            const categoryRecord = await db.select().from(category).where(eq(category.id, categoryId)).get();
            if (!categoryRecord) {
                throw new Error('分类不存在');
            }
            return categoryRecord;
        } catch (error) {
            console.error('Database query error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Database query failed: ${errorMessage}`);
        }
    }
} 