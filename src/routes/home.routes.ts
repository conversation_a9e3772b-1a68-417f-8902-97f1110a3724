import { Hono } from 'hono';
import { HomeHandler } from '../handlers/home.handler';
import { authMiddleware } from '../middleware/auth.middleware';

const homeRoutes = new Hono<{ Bindings: Env }>();
const homeHandler = new HomeHandler();

// 获取Banner书籍列表（阅读次数最多的3条）- 需要认证
homeRoutes.get('/banner', authMiddleware, homeHandler.getBanner);

// 获取推荐书籍列表（喜欢次数最多的3条）- 需要认证
homeRoutes.get('/recommend', authMiddleware, homeHandler.getRecommend);

// 获取用户最近阅读记录（需要认证）
homeRoutes.get('/recent-read', authMiddleware, homeHandler.getRecentRead);

// 获取游客Banner书籍列表
homeRoutes.get('/guest/banner', homeHandler.getGuestBanner);

// 获取游客推荐书籍列表
homeRoutes.get('/guest/recommend', homeHandler.getGuestRecommend);

export { homeRoutes };
