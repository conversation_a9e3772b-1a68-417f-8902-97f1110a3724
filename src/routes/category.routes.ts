import { Hono } from 'hono';
import { CategoryHandler } from '../handlers/category.handler';

const categoryRoutes = new Hono<{ Bindings: Env }>();
const categoryHandler = new CategoryHandler();

// 新增分类
categoryRoutes.post('/', categoryHandler.createCategory);

// 修改分类
categoryRoutes.post('/update/:id', categoryHandler.updateCategory);

// 删除分类
categoryRoutes.post('/delete/:id', categoryHandler.deleteCategory);

// 查询所有分类 - 支持多个路径
categoryRoutes.get('/', categoryHandler.getAllCategories);
categoryRoutes.get('/list', categoryHandler.getAllCategories);

// 根据ID查询分类 - 放在具体路由之后
categoryRoutes.get('/:id', categoryHandler.getCategoryById);

export { categoryRoutes }; 