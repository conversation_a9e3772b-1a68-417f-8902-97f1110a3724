import { Hono } from "hono";
import { AuthService } from "../services/auth.service";
import { AuthHandler } from "../handlers/auth.handler";
import { authMiddleware } from "../middleware/auth.middleware";

const authRoutes = new Hono<{ Bindings: Env }>();

const authService = new AuthService();
const authHandler = new AuthHandler(authService);

// Apple 登录端点 - 对应 Java 项目中的 @PostMapping("/apple")
authRoutes.post('/apple', (ctx) => authHandler.appleLogin(ctx));

// 原有端点保持向后兼容
authRoutes.post('/login', (ctx) => authHandler.login(ctx));
authRoutes.post('/logout', (ctx) => authHandler.logout(ctx));
authRoutes.get('/foo', authHandler.foo);

// 获取用户信息（需要认证）
authRoutes.get('/user/info', authMiddleware, authHandler.getUserInfo);

// 更新用户信息（需要认证）
authRoutes.post('/user/update', authMiddleware, authHandler.updateUserInfo);

export { authRoutes };