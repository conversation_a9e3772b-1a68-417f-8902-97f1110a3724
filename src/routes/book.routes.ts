import { Hono } from 'hono';
import { BookHandler } from '../handlers/book.handler';
import { authMiddleware } from '../middleware/auth.middleware';

const bookRoutes = new Hono<{ Bindings: Env }>();
const bookHandler = new BookHandler();

// 上传书籍
bookRoutes.post('/', bookHandler.uploadBook);

// 编辑书籍
bookRoutes.post('/update/:id', bookHandler.updateBook);

// 删除书籍
bookRoutes.post('/delete/:id', bookHandler.deleteBook);

// 获取书籍列表
bookRoutes.get('/list', bookHandler.getBookList);

// 根据分类获取书籍列表
bookRoutes.get('/category', bookHandler.getBooksByCategory);

// 获取书籍详情
bookRoutes.get('/detail/:id', bookHandler.getBookDetail);

// 获取书籍URL
bookRoutes.get('/:id/url', bookHandler.getBookUrl);

// 用户为书籍评分（需要认证）
bookRoutes.post('/rating', authMiddleware, bookHandler.rateBook);

// 更新用户阅读进度（需要认证）
bookRoutes.post('/read-progress', authMiddleware, bookHandler.updateReadProgress);

export { bookRoutes };
