import { Hono } from 'hono';

const docsRouter = new Hono();

// 获取 API 文档页面
docsRouter.get('/', async (c) => {
    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KR API 文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .nav {
            background: #f1f3f4;
            padding: 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
        }
        .nav li {
            border-right: 1px solid #e0e0e0;
        }
        .nav a {
            display: block;
            padding: 15px 20px;
            text-decoration: none;
            color: #666;
            transition: all 0.3s;
        }
        .nav a:hover, .nav a.active {
            background: #667eea;
            color: white;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .endpoint {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .endpoint-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .endpoint-header:hover {
            background: #e9ecef;
        }
        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8em;
            margin-right: 15px;
        }
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        .endpoint-path {
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 1.1em;
            color: #333;
        }
        .endpoint-description {
            color: #666;
            margin-left: auto;
            margin-right: 20px;
        }
        .expand-icon {
            transition: transform 0.3s;
        }
        .endpoint-body {
            padding: 20px;
            display: none;
            background: white;
        }
        .endpoint-body.show {
            display: block;
        }
        .endpoint.expanded .expand-icon {
            transform: rotate(180deg);
        }
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .params-table th,
        .params-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        .params-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
        .response-example {
            background: #f0f8ff;
            border-left: 4px solid #007bff;
        }
        .tag {
            display: inline-block;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 KR API 文档</h1>
            <p>知识阅读平台 RESTful API 接口文档</p>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="#books" class="active">📖 书籍管理</a></li>
                <li><a href="#categories">📂 分类管理</a></li>
                <li><a href="#config">⚙️ 配置管理</a></li>
                <li><a href="#overview">🔍 接口概览</a></li>
            </ul>
        </nav>

        <div class="content">
            <!-- 书籍管理接口 -->
            <section id="books" class="section">
                <h2>📖 书籍管理接口</h2>
                
                <!-- 上传书籍 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/book</span>
                        </div>
                        <span class="endpoint-description">上传新书籍</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>上传书籍文件（PDF）和封面图片到 R2 存储，并保存书籍信息到数据库</p>
                        
                        <h4>请求参数 (FormData)</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>title</td><td>string</td><td class="required">是</td><td>书籍标题</td></tr>
                            <tr><td>categoryId</td><td>string</td><td class="required">是</td><td>分类ID</td></tr>
                            <tr><td>pdf</td><td>File</td><td class="required">是</td><td>PDF 文件</td></tr>
                            <tr><td>cover</td><td>File</td><td class="required">是</td><td>封面图片</td></tr>
                            <tr><td>isFree</td><td>boolean</td><td>否</td><td>是否免费 (true/false)</td></tr>
                        </table>

                        <h4>响应示例</h4>
                        <div class="code-block response-example">
{
  "success": true,
  "data": [{
    "id": "abc123",
    "title": "示例书籍",
    "categoryId": "1",
    "cover": "https://res.youggst.com/abc123-cover.jpg",
    "url": "https://res.youggst.com/abc123-book.pdf",
    "isFree": 1,
    "level": 1,
    "readCount": 0,
    "favoriteCount": 0
  }]
}
                        </div>
                    </div>
                </div>

                <!-- 编辑书籍 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/book/update/:id</span>
                        </div>
                        <span class="endpoint-description">编辑书籍信息</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>更新书籍信息，支持更新文本信息和文件</p>
                        
                        <h4>路径参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>id</td><td>string</td><td class="required">是</td><td>书籍ID</td></tr>
                        </table>

                        <h4>请求参数 (FormData)</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>title</td><td>string</td><td class="required">是</td><td>书籍标题</td></tr>
                            <tr><td>categoryId</td><td>string</td><td>否</td><td>分类ID</td></tr>
                            <tr><td>isFree</td><td>boolean</td><td>否</td><td>是否免费</td></tr>
                            <tr><td>level</td><td>number</td><td>否</td><td>书籍等级</td></tr>
                            <tr><td>pdf</td><td>File</td><td>否</td><td>新的PDF文件</td></tr>
                            <tr><td>cover</td><td>File</td><td>否</td><td>新的封面图片</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 删除书籍 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/book/delete/:id</span>
                        </div>
                        <span class="endpoint-description">删除书籍</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>删除书籍及其在 R2 存储中的文件</p>
                        
                        <h4>路径参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>id</td><td>string</td><td class="required">是</td><td>书籍ID</td></tr>
                        </table>

                        <h4>响应示例</h4>
                        <div class="code-block response-example">
{
  "success": true,
  "data": {
    "success": true,
    "message": "书籍删除成功",
    "deletedFiles": ["abc123-book.pdf", "abc123-cover.jpg"]
  }
}
                        </div>
                    </div>
                </div>

                <!-- 获取书籍列表 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/book/list</span>
                        </div>
                        <span class="endpoint-description">获取书籍列表</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>分页获取书籍列表</p>
                        
                        <h4>查询参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>page</td><td>number</td><td>否</td><td>页码，默认 1</td></tr>
                            <tr><td>pageSize</td><td>number</td><td>否</td><td>每页数量，默认 10</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 获取书籍URL -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/book/:id/url</span>
                        </div>
                        <span class="endpoint-description">获取书籍文件URL</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>获取指定书籍的PDF文件访问URL</p>
                        
                        <h4>路径参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>id</td><td>string</td><td class="required">是</td><td>书籍ID</td></tr>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 分类管理接口 -->
            <section id="categories" class="section">
                <h2>📂 分类管理接口</h2>
                
                <!-- 新增分类 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/category</span>
                        </div>
                        <span class="endpoint-description">新增分类</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>创建新的书籍分类</p>
                        
                        <h4>请求参数 (FormData)</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>categoryName</td><td>string</td><td class="required">是</td><td>分类名称</td></tr>
                            <tr><td>cover</td><td>File</td><td>否</td><td>分类封面图片</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 修改分类 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/category/update/:id</span>
                        </div>
                        <span class="endpoint-description">修改分类</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>更新指定分类的信息</p>
                        
                        <h4>路径参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>id</td><td>string</td><td class="required">是</td><td>分类ID</td></tr>
                        </table>

                        <h4>请求参数 (FormData)</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>categoryName</td><td>string</td><td class="required">是</td><td>分类名称</td></tr>
                            <tr><td>cover</td><td>File</td><td>否</td><td>分类封面图片</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 删除分类 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/category/delete/:id</span>
                        </div>
                        <span class="endpoint-description">删除分类</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>删除指定分类（需确保没有关联的书籍）</p>
                        
                        <h4>路径参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>id</td><td>string</td><td class="required">是</td><td>书籍ID</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 查询所有分类 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/category</span>
                        </div>
                        <span class="endpoint-description">获取所有分类</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>获取所有书籍分类列表</p>
                        
                        <h4>响应示例</h4>
                        <div class="code-block response-example">
{
  "success": true,
  "data": [
    {
      "id": "1",
      "categoryName": "编程技术",
      "cover": "https://res.youggst.com/cat1-cover.jpg",
      "createTime": 1640995200,
      "updateTime": 1640995200
    }
  ]
}
                        </div>
                    </div>
                </div>

                <!-- 根据ID查询分类 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/category/:id</span>
                        </div>
                        <span class="endpoint-description">根据ID获取分类</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>根据分类ID获取特定分类信息</p>
                        
                        <h4>路径参数</h4>
                        <table class="params-table">
                            <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
                            <tr><td>id</td><td>string</td><td class="required">是</td><td>分类ID</td></tr>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 配置管理接口 -->
            <section id="config" class="section">
                <h2>⚙️ 配置管理接口</h2>
                
                <!-- 获取游客Banner配置 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/config/guest/banner</span>
                        </div>
                        <span class="endpoint-description">获取游客Banner配置</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>获取未登录用户能看到的Banner轮播图配置</p>
                        
                        <h4>响应示例</h4>
                        <div class="code-block response-example">
{
  "success": true,
  "data": [
    "banner1.jpg",
    "banner2.jpg", 
    "banner3.jpg"
  ]
}
                        </div>
                    </div>
                </div>

                <!-- 获取游客推荐配置 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/config/guest/recommend</span>
                        </div>
                        <span class="endpoint-description">获取游客推荐配置</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>获取未登录用户能看到的推荐内容配置</p>
                        
                        <h4>响应示例</h4>
                        <div class="code-block response-example">
{
  "success": true,
  "data": [
    "book1",
    "book2",
    "book3",
    "book4",
    "book5"
  ]
}
                        </div>
                    </div>
                </div>

                <!-- 获取所有游客配置 -->
                <div class="endpoint">
                    <div class="endpoint-header" onclick="toggleEndpoint(this)">
                        <div>
                            <span class="method get">GET</span>
                            <span class="endpoint-path">/config/guest</span>
                        </div>
                        <span class="endpoint-description">获取所有游客配置</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>描述：</strong>一次性获取所有游客相关的配置信息</p>
                        
                        <h4>响应示例</h4>
                        <div class="code-block response-example">
{
  "success": true,
  "data": {
    "banner": [
      "banner1.jpg",
      "banner2.jpg",
      "banner3.jpg"
    ],
    "recommend": [
      "book1",
      "book2", 
      "book3",
      "book4",
      "book5"
    ]
  }
}
                        </div>
                    </div>
                </div>
            </section>

            <!-- 接口概览 -->
            <section id="overview" class="section">
                <h2>🔍 接口概览</h2>
                <div class="code-block">
                    <strong>基础URL:</strong> http://localhost:8787 (开发环境)
                    <br><strong>内容类型:</strong> multipart/form-data (文件上传), application/json (其他)
                    <br><strong>响应格式:</strong> JSON
                </div>

                <h3>🎯 通用响应格式</h3>
                <div class="code-block">
// 成功响应
{
  "success": true,
  "data": { ... }
}

// 错误响应
{
  "success": false,
  "error": "错误信息"
}
                </div>

                <h3>📊 接口统计</h3>
                <table class="params-table">
                    <tr><th>功能模块</th><th>接口数量</th><th>说明</th></tr>
                    <tr><td>📖 书籍管理</td><td>5个</td><td>上传、编辑、删除、列表、获取URL</td></tr>
                    <tr><td>📂 分类管理</td><td>5个</td><td>增删改查分类信息</td></tr>
                    <tr><td>⚙️ 配置管理</td><td>3个</td><td>游客Banner、推荐、完整配置</td></tr>
                    <tr><td>📝 总计</td><td>13个</td><td>覆盖完整的 CRUD 和配置管理</td></tr>
                </table>
            </section>
        </div>
    </div>

    <script>
        function toggleEndpoint(header) {
            const endpoint = header.parentElement;
            const body = endpoint.querySelector('.endpoint-body');
            
            endpoint.classList.toggle('expanded');
            body.classList.toggle('show');
        }

        // 导航切换
        document.querySelectorAll('.nav a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活动状态
                document.querySelectorAll('.nav a').forEach(a => a.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');
                
                // 滚动到对应章节
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // 默认展开第一个接口
        document.querySelector('.endpoint-header').click();
    </script>
</body>
</html>`;
    
    return c.html(htmlContent);
});

export { docsRouter }; 