import { Hono } from 'hono';
import { UserHandler } from '../handlers/user.handler';
import { authMiddleware } from '../middleware/auth.middleware';

const userRoutes = new Hono<{ Bindings: Env }>();
const userHandler = new UserHandler();

// 用户收藏书籍（需要认证）
userRoutes.post('/favorite/add', authMiddleware, userHandler.addFavorite);

// 用户删除收藏书籍（需要认证）
userRoutes.post('/favorite/delete', authMiddleware, userHandler.deleteFavorite);

// 检查用户是否收藏过指定书籍（需要认证）
userRoutes.post('/favorite/check', authMiddleware, userHandler.checkFavorite);

// 获取用户的收藏列表（需要认证）
userRoutes.get('/favorite/list', authMiddleware, userHandler.getFavoriteList);

// 获取用户的历史阅读列表（需要认证）
userRoutes.get('/history/list', authMiddleware, userHandler.getHistoryList);

// 获取用户信息（需要认证）
userRoutes.get('/info', authMiddleware, userHandler.getUserInfo);


// 更新用户头像（需要认证）
userRoutes.post('/avatar/update', authMiddleware, userHandler.updateAvatar);

export { userRoutes };
