import type { AppleTokenPayload } from '../types/auth.types';

/**
 * Apple 认证工具类
 * 复刻 Java 项目中的 Apple Identity Token 验证逻辑
 */
export class AppleAuthUtil {
  private readonly appleIssuer: string;
  private readonly clientId: string;

  constructor(appleIssuer: string, clientId: string) {
    this.appleIssuer = appleIssuer;
    this.clientId = clientId;
  }

  /**
   * 解析 Apple Identity Token
   * 对应 Java 项目中的 JWT.decode(identityToken) 逻辑
   * @param identityToken Apple Identity Token
   * @returns 解析后的 payload
   */
  decodeAppleToken(identityToken: string): AppleTokenPayload {
    try {
      const parts = identityToken.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      // Base64 URL decode payload
      const payload = this.base64UrlDecode(parts[1]);
      const parsedPayload = JSON.parse(payload);

      return parsedPayload as AppleTokenPayload;
    } catch (error) {
      console.error('Failed to decode Apple token:', error);
      throw new Error(`Failed to decode Apple token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 验证 Apple Identity Token
   * 复刻 Java 项目中的验证逻辑：
   * 1. 验证 issuer
   * 2. 验证 audience
   * 3. 验证过期时间
   * @param payload Apple token payload
   */
  validateAppleToken(payload: AppleTokenPayload): void {
    // 验证 issuer - 对应 Java 中的 !issuer.equals(appleAuthConfig.getIssuer())
    if (payload.iss !== this.appleIssuer) {
      throw new Error(`Apple 登录失败 Invalid issuer. Expected: ${this.appleIssuer}, Got: ${payload.iss}`);
    }

    // 验证 audience - 对应 Java 中的 !audience.equals(appleAuthConfig.getClientId())
    if (payload.aud !== this.clientId) {
      throw new Error(`Apple 登录失败 Invalid audience. Expected: ${this.clientId}, Got: ${payload.aud}`);
    }

    // 验证过期时间
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp <= now) {
      throw new Error('Apple 登录失败 Token has expired');
    }

    console.log('Apple token validation successful:', {
      issuer: payload.iss,
      audience: payload.aud,
      subject: payload.sub,
      expiresAt: new Date(payload.exp * 1000).toISOString()
    });
  }

  /**
   * Base64 URL 解码
   * @param base64Url Base64 URL 编码的字符串
   * @returns 解码后的字符串
   */
  private base64UrlDecode(base64Url: string): string {
    // 将 Base64 URL 转换为标准 Base64
    let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    
    // 补齐填充字符
    const padding = base64.length % 4;
    if (padding) {
      base64 += '='.repeat(4 - padding);
    }
    
    try {
      // 在 Cloudflare Workers 环境中使用 atob
      return atob(base64);
    } catch (error) {
      throw new Error('Invalid Base64 encoding');
    }
  }
}
