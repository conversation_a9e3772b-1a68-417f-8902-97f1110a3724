import { sign, verify } from 'hono/jwt'

// Token 配置
const JWT_SECRET = 'your-super-secret-key-please-change-in-production'
// Token 过期时间（30天）
const TOKEN_EXPIRATION = 30 * 24 * 60 * 60

/**
 * 生成JWT token
 * @param userId 用户ID
 * @returns JWT token字符串
 */
export const generateToken = async (userId: string): Promise<string> => {
    const exp = Math.floor(Date.now() / 1000) + TOKEN_EXPIRATION;
    const token = await sign({ userId, exp }, JWT_SECRET);
    return token;
}

/**
 * 验证token是否有效
 * @param token JWT token字符串
 * @returns 是否有效
 */
export const verifyToken = async (token: string): Promise<boolean> => {
    try {
        await verify(token, JWT_SECRET);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 从token中提取userId
 * @param token JWT token字符串
 * @returns 用户ID，如果token无效则返回null
 */
export const extractUserId = async (token: string): Promise<string | null> => {
    try {
        const payload = await verify(token, JWT_SECRET);
        return payload.userId as string;
    } catch (error) {
        return null;
    }
} 