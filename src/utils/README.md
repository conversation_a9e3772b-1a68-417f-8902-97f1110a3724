# HTTP 工具类使用文档

## 概述

这是一个基于 axios 的网络请求工具类，专门为 Cloudflare Workers 环境设计，提供了完整的 HTTP 请求功能，包括请求拦截器、响应拦截器、错误处理等。

## 特性

- ✅ 基于 axios，功能强大
- ✅ 完整的 TypeScript 支持
- ✅ 请求/响应拦截器
- ✅ 统一的错误处理
- ✅ 支持文件上传/下载
- ✅ 适配 Cloudflare Workers 环境
- ✅ 可配置的 loading 和错误提示

## 快速开始

### 1. 基本使用

```typescript
import { http } from '../utils/http';

// GET 请求
const response = await http.get('https://api.example.com/users');
console.log(response.data);

// POST 请求
const newUser = await http.post('https://api.example.com/users', {
  name: '张三',
  email: '<EMAIL>'
});
```

### 2. 使用 API 服务类

```typescript
import { userApi, postApi } from '../services/api';

// 获取用户列表
const users = await userApi.getUsers();

// 创建新用户
const newUser = await userApi.createUser({
  name: '李四',
  email: '<EMAIL>'
});
```

## API 参考

### HttpService 类

#### 构造函数

```typescript
new HttpService(baseURL?: string, timeout?: number)
```

- `baseURL`: 基础 URL，默认为空字符串
- `timeout`: 请求超时时间（毫秒），默认 10000ms

#### 方法

##### GET 请求
```typescript
async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>
```

##### POST 请求
```typescript
async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>
```

##### PUT 请求
```typescript
async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>
```

##### DELETE 请求
```typescript
async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>
```

##### PATCH 请求
```typescript
async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>
```

##### 文件上传
```typescript
async upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<ApiResponse<T>>
```

##### 文件下载
```typescript
async download(url: string, config?: RequestConfig): Promise<Response>
```

### 配置选项

#### RequestConfig

```typescript
interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;  // 是否显示 loading，默认 true
  showError?: boolean;    // 是否显示错误提示，默认 true
}
```

#### ApiResponse

```typescript
interface ApiResponse<T = any> {
  code: number;      // 状态码
  message: string;   // 消息
  data: T;          // 数据
  success: boolean;  // 是否成功
}
```

## 使用示例

### 1. 创建自定义实例

```typescript
import { createHttp } from '../utils/http';

// 创建带基础 URL 的实例
const apiClient = createHttp('https://api.example.com', 15000);

// 使用
const response = await apiClient.get('/users');
```

### 2. 错误处理

```typescript
try {
  const response = await http.get('https://api.example.com/users');
  console.log(response.data);
} catch (error: any) {
  if (error.response) {
    console.error('HTTP 错误:', error.response.status);
  } else if (error.request) {
    console.error('网络错误:', error.message);
  } else {
    console.error('其他错误:', error.message);
  }
}
```

### 3. 文件上传

```typescript
import { fileApi } from '../services/api';

// 创建 FormData
const formData = new FormData();
formData.append('file', fileBlob, 'filename.txt');

// 上传文件
const response = await fileApi.uploadFile(formData);
console.log('文件 URL:', response.data.url);
```

### 4. 并发请求

```typescript
import { userApi, postApi } from '../services/api';

// 并发发送多个请求
const [users, posts] = await Promise.all([
  userApi.getUsers(),
  postApi.getPosts()
]);
```

## 拦截器

### 请求拦截器

自动添加以下功能：
- Authorization 头（如果存在 token）
- Loading 状态管理
- 请求日志记录

### 响应拦截器

自动处理以下情况：
- 业务错误（success: false）
- HTTP 状态码错误
- 网络连接错误
- Loading 状态管理

## 错误处理

工具类会自动处理以下错误：

- **400**: 请求参数错误
- **401**: 未授权，自动清除 token
- **403**: 拒绝访问
- **404**: 请求地址不存在
- **500**: 服务器内部错误
- **网络错误**: 连接失败

## 自定义配置

### 修改 token 获取方式

```typescript
// 在 HttpService 类中修改 getToken 方法
private getToken(): string | null {
  // 从环境变量获取
  return process.env.API_TOKEN || null;
  
  // 或从其他来源获取
  // return this.tokenStorage.getToken();
}
```

### 自定义错误处理

```typescript
// 在 HttpService 类中修改 showError 方法
private showError(message: string) {
  // 发送错误到监控服务
  console.error('API 错误:', message);
  
  // 或发送到日志服务
  // this.logger.error(message);
}
```

## 最佳实践

1. **使用 API 服务类**：将 API 调用封装在服务类中，便于维护和复用
2. **统一错误处理**：利用拦截器统一处理错误，避免重复代码
3. **类型安全**：为 API 响应定义明确的类型接口
4. **超时设置**：根据实际需求设置合适的超时时间
5. **并发控制**：使用 Promise.all 进行并发请求，提高性能

## 注意事项

1. 此工具类专为 Cloudflare Workers 环境设计，移除了浏览器相关的 API
2. 文件上传需要传入 FormData 对象
3. 文件下载返回 Response 对象，需要调用方自行处理
4. Token 管理需要根据实际需求进行自定义
5. Loading 和错误提示功能需要根据实际 UI 框架进行适配 