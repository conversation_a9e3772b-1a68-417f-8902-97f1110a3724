/**
 * 日期工具类，类似Java项目中的LocalTimeUtil
 */
export class DateUtil {
    /**
     * 计算从指定时间到现在的天数
     * 对应Java项目中的LocalTimeUtil.getDaysBetween()
     * 
     * @param startTime 开始时间（用户注册时间）
     * @returns 天数差
     */
    static getDaysBetween(startTime: Date): number {
        const now = new Date();
        const start = new Date(startTime);
        
        // 计算时间差（毫秒）
        const timeDiff = now.getTime() - start.getTime();
        
        // 转换为天数，向下取整
        const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
        
        // 至少返回1天（即使是同一天注册）
        return Math.max(dayDiff, 1);
    }

    /**
     * 处理头像URL，如果为空则返回默认头像
     * 
     * @param avatar 用户头像URL
     * @returns 处理后的头像URL
     */
    static processAvatarUrl(avatar?: string | null): string {
        const defaultAvatarUrl = 'https://res.youggst.com/sw_default_avatar.webp';
        
        // 如果avatar为空、null或undefined，返回默认头像
        if (!avatar || avatar.trim() === '') {
            return defaultAvatarUrl;
        }
        
        return avatar;
    }

    /**
     * 将日期格式化为 yyyy-MM-dd HH:mm:ss 格式
     * 
     * @param date 要格式化的日期，可以是 Date 对象、时间戳或 ISO 字符串
     * @returns 格式化后的日期字符串，如果输入为空则返回 null
     */
    static formatToCustomDateTime(date?: Date | number | string | null): string | null {
        if (!date) {
            return null;
        }

        let dateObj: Date;
        
        if (date instanceof Date) {
            dateObj = date;
        } else if (typeof date === 'number') {
            // 如果数字小于 10^10，假设是秒时间戳，否则是毫秒时间戳
            dateObj = date < 10000000000 ? new Date(date * 1000) : new Date(date);
        } else if (typeof date === 'string') {
            dateObj = new Date(date);
        } else {
            return null;
        }

        // 检查日期是否有效
        if (isNaN(dateObj.getTime())) {
            return null;
        }

        // 格式化为 yyyy-MM-dd HH:mm:ss
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getDate()).padStart(2, '0');
        const hours = String(dateObj.getHours()).padStart(2, '0');
        const minutes = String(dateObj.getMinutes()).padStart(2, '0');
        const seconds = String(dateObj.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
}
