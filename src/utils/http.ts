import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
  showError?: boolean;
}

// HTTP 工具类
export class HttpService {
  private instance: AxiosInstance;

  constructor(baseURL: string = '', timeout: number = 10000) {
    this.instance = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  // 设置拦截器
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // 可以在这里添加 token
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 可以在这里添加 loading 状态
        const requestConfig = config as RequestConfig;
        if (requestConfig.showLoading !== false) {
          this.showLoading();
        }

        return config;
      },
      (error) => {
        this.hideLoading();
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        this.hideLoading();

        // 处理业务错误
        if (response.data && !response.data.success) {
          const error = new Error(response.data.message || '请求失败');
          (error as any).code = response.data.code;
          return Promise.reject(error);
        }

        return response;
      },
      (error) => {
        this.hideLoading();

        // 处理 HTTP 错误
        let message = '网络错误';
        if (error.response) {
          switch (error.response.status) {
            case 400:
              message = '请求参数错误';
              break;
            case 401:
              message = '未授权，请重新登录';
              this.handleUnauthorized();
              break;
            case 403:
              message = '拒绝访问';
              break;
            case 404:
              message = '请求地址不存在';
              break;
            case 500:
              message = '服务器内部错误';
              break;
            default:
              message = `连接错误${error.response.status}`;
          }
        } else if (error.request) {
          message = '网络连接失败';
        }

        // 显示错误信息
        const requestConfig = error.config as RequestConfig;
        if (requestConfig?.showError !== false) {
          this.showError(message);
        }

        return Promise.reject(error);
      }
    );
  }

  // 获取 token（可以根据实际情况修改）
  private getToken(): string | null {
    // 在 Cloudflare Workers 环境中，可以从环境变量或其他地方获取 token
    // 这里返回 null，你可以根据实际需求修改
    return null;
  }

  // 处理未授权
  private handleUnauthorized() {
    // 在服务器端环境中处理未授权
    console.log('处理未授权请求');
  }

  // 显示 loading
  private showLoading() {
    // 在服务器端环境中，可以记录日志或发送信号
    console.log('显示 loading...');
  }

  // 隐藏 loading
  private hideLoading() {
    // 在服务器端环境中，可以记录日志或发送信号
    console.log('隐藏 loading...');
  }

  // 显示错误信息
  private showError(message: string) {
    // 在服务器端环境中，可以记录日志或发送错误通知
    console.error('错误信息:', message);
  }

  // GET 请求
  async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  // POST 请求
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  // PUT 请求
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  // DELETE 请求
  async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  // PATCH 请求
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  // 上传文件（在 Cloudflare Workers 中，文件上传通常通过 FormData 处理）
  async upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 下载文件（返回 Response 对象，由调用方处理）
  async download(url: string, config?: RequestConfig): Promise<Response> {
    const response = await this.instance.get(url, {
      ...config,
      responseType: 'arraybuffer',
    });

    // 返回标准的 Response 对象
    return new Response(response.data, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as any,
    });
  }
}

// 创建默认实例
export const http = new HttpService();

// 创建带基础 URL 的实例
export const createHttp = (baseURL: string, timeout?: number) => {
  return new HttpService(baseURL, timeout);
};

// 导出类型
export type { AxiosInstance, AxiosRequestConfig, AxiosResponse }; 