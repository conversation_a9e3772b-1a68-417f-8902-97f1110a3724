/**
 * CREATE TABLE `quiz_record` (
  `id` text NOT NULL,
  `user_id` text NOT NULL,
  `quiz_id` text NOT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
 */

import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 小测验记录表
export const quizRecord = sqliteTable('quiz_record', {
  id: text('id').primaryKey(),
  userId: text('user_id'),
  quizId: text('quiz_id'),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});