/**
 * CREATE TABLE `favorite` (
  `id` text NOT NULL,
  `user_id` text NOT NULL,
  `book_id` text NOT NULL,
  `progress` int DEFAULT '0',
  `last_viewed_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
  */

import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 收藏表
export const favorite = sqliteTable('favorite', {
  id: text('id').primaryKey(),
  userId: text('user_id'),
  bookId: text('book_id'),
  progress: integer('progress').default(0),
  lastViewedTime: integer('last_viewed_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});