/**
 * CREATE TABLE `quiz` (
  `id` text NOT NULL,
  `book_id` text NOT NULL,
  `content` text NOT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
  */

import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 小测验表
export const quiz = sqliteTable('quiz', {
  id: text('id').primaryKey(),
  bookId: text('book_id'),
  content: text('content'),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});