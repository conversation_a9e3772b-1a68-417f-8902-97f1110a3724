/**
 * CREATE TABLE `rating` (
  `id` text NOT NULL,
  `user_id` text NOT NULL,
  `book_id` text NOT NULL,
  `score` int NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  CONSTRAINT `rating_chk_1` CHECK (((`score` >= 1) and (`score` <= 5)))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
 */

import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 评分表
export const rating = sqliteTable('rating', {
  id: text('id').primaryKey(),
  userId: text('user_id'),
  bookId: text('book_id'),
  score: integer('score'),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});