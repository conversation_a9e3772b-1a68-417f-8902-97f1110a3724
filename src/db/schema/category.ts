/**
 * CREATE TABLE `category` (
  `id` text NOT NULL,
  `category_name` varchar(32) DEFAULT NULL,
  `cover` varchar(255) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
 */

import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 分类表
export const category = sqliteTable('category', {
  id: text('id').primaryKey(),
  categoryName: text('category_name', { length: 32 }),
  cover: text('cover', { length: 255 }),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});