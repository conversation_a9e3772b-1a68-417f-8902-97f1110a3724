import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 用户表
export const user = sqliteTable('user', {
  id: text('id', { length: 36 }).unique().primary<PERSON>ey(),
  appleAuthId: text('apple_auth_id', { length: 255 }).unique(),
  name: text('name', { length: 100 }).notNull(),
  avatar: text('avatar', { length: 255 }),
  token: text('token', { length: 255 }).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
});