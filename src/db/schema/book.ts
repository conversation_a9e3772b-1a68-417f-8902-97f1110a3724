/**
 * CREATE TABLE `book` (
  `id` text NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `cover` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `level` int DEFAULT NULL,
  `category_id` text DEFAULT NULL,
  `read_count` int DEFAULT '0',
  `favorite_count` int DEFAULT '0',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_free` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
 */

import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from 'drizzle-orm';

// 书籍表
export const book = sqliteTable('book', {
  id: text('id').primaryKey(),
  title: text('title', { length: 255 }),
  cover: text('cover', { length: 255 }),
  url: text('url', { length: 255 }),
  level: integer('level'),
  categoryId: text('category_id'),
  readCount: integer('read_count').default(0),
  favoriteCount: integer('favorite_count').default(0),
  createTime: integer('create_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  updateTime: integer('update_time', { mode: 'timestamp' }).notNull().default(sql`(unixepoch())`),
  isFree: integer('is_free').default(0),
});
