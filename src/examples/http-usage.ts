import { http, createHttp } from '../utils/http';
import { userApi, postApi, fileApi } from '../services/api';

// 示例 1: 使用默认 HTTP 实例
export async function exampleBasicUsage() {
  try {
    // GET 请求
    const response = await http.get('https://api.example.com/users');
    console.log('用户列表:', response.data);

    // POST 请求
    const newUser = await http.post('https://api.example.com/users', {
      name: '张三',
      email: '<EMAIL>'
    });
    console.log('创建用户:', newUser.data);

  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 示例 2: 创建自定义 HTTP 实例
export async function exampleCustomHttp() {
  // 创建带基础 URL 的实例
  const apiClient = createHttp('https://api.example.com', 15000);

  try {
    const response = await apiClient.get('/posts');
    console.log('文章列表:', response.data);
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 示例 3: 使用 API 服务类
export async function exampleApiServices() {
  try {
    // 获取用户列表
    const usersResponse = await userApi.getUsers();
    console.log('用户列表:', usersResponse.data);

    // 创建新用户
    const newUserResponse = await userApi.createUser({
      name: '李四',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg'
    });
    console.log('新用户:', newUserResponse.data);

    // 获取文章列表
    const postsResponse = await postApi.getPosts();
    console.log('文章列表:', postsResponse.data);

    // 创建新文章
    const newPostResponse = await postApi.createPost({
      title: '我的第一篇文章',
      content: '这是文章内容...',
      author: '李四'
    });
    console.log('新文章:', newPostResponse.data);

  } catch (error) {
    console.error('API 调用失败:', error);
  }
}

// 示例 4: 文件上传
export async function exampleFileUpload() {
  try {
    // 创建 FormData（在实际使用中，这通常来自文件输入）
    const formData = new FormData();
    formData.append('file', new Blob(['文件内容'], { type: 'text/plain' }), 'test.txt');

    // 上传文件
    const uploadResponse = await fileApi.uploadFile(formData);
    console.log('文件上传成功:', uploadResponse.data.url);

  } catch (error) {
    console.error('文件上传失败:', error);
  }
}

// 示例 5: 错误处理
export async function exampleErrorHandling() {
  try {
    // 这个请求会失败（404）
    const response = await http.get('https://api.example.com/nonexistent');
    console.log('响应:', response);
  } catch (error: any) {
    if (error.response) {
      console.error('HTTP 错误:', error.response.status, error.response.statusText);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
  }
}

// 示例 6: 带配置的请求
export async function exampleWithConfig() {
  try {
    // 禁用 loading 和错误提示
    const response = await http.get('https://api.example.com/users', {
      showLoading: false,
      showError: false,
      timeout: 5000
    });
    console.log('响应:', response.data);
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 示例 7: 并发请求
export async function exampleConcurrentRequests() {
  try {
    // 并发发送多个请求
    const [usersResponse, postsResponse] = await Promise.all([
      userApi.getUsers(),
      postApi.getPosts()
    ]);

    console.log('用户数量:', usersResponse.data.length);
    console.log('文章数量:', postsResponse.data.length);
  } catch (error) {
    console.error('并发请求失败:', error);
  }
} 