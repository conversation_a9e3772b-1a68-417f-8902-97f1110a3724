/**
 * Apple 登录请求 DTO
 * 对应 Java 项目中的 AppleAuthDTO
 */
export interface AppleAuthDTO {
  identityToken: string;
  givenName?: string;
  familyName?: string;
  email?: string;
}

/**
 * 认证返回 VO
 * 对应 Java 项目中的 AuthVO
 */
export interface AuthVO {
  userId: string;
  token: string;
  isNew: number; // 1: 新用户, 0: 已存在用户
}

/**
 * Apple Identity Token 解析后的 Payload
 */
export interface AppleTokenPayload {
  iss: string;        // issuer - Apple 签发者
  aud: string;        // audience - 客户端 ID
  sub: string;        // subject - Apple 用户 ID
  email?: string;     // 用户邮箱（可选）
  email_verified?: boolean; // 邮箱是否验证
  exp: number;        // 过期时间
  iat: number;        // 签发时间
  nonce?: string;     // 防重放攻击
  at_hash?: string;   // Access Token hash
}

/**
 * 用户创建数据
 */
export interface UserCreateInput {
  id: string;
  appleAuthId: string;
  name: string;
  avatar: string;
  token: string;
}
