export interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
}

export class ResponseUtil {
    static success<T>(data: T, message: string = "success"): ApiResponse<T> {
        return {
            code: 200,
            message,
            data
        };
    }

    static error(message: string, code: number = 500): ApiResponse<null> {
        return {
            code,
            message,
            data: null
        };
    }

    static badRequest(message: string): ApiResponse<null> {
        return {
            code: 400,
            message,
            data: null
        };
    }

    static notFound(message: string): ApiResponse<null> {
        return {
            code: 404,
            message,
            data: null
        };
    }
} 