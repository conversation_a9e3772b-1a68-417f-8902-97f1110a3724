import { Context, Next } from 'hono';
import { extractUserId } from '../utils/token';
import { ResponseUtil } from '../types/response';

// 扩展Context类型，添加userId
declare module 'hono' {
  interface ContextVariableMap {
    userId: string;
  }
}

/**
 * 认证中间件
 * 从Authorization header中提取Token，验证并设置用户ID到Context中
 * 如果Token无效或不存在，直接返回401错误
 */
export const authMiddleware = async (c: Context<{ Bindings: Env }>, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('No Authorization header or invalid format');
      return c.json(ResponseUtil.error("Token不存在或格式错误", 401), 401);
    }
    
    const token = authHeader.substring(7);
    const userId = await extractUserId(token);
    
    if (!userId) {
      console.log('Invalid token provided');
      return c.json(ResponseUtil.error("Token无效或已过期", 401), 401);
    }
    
    // Token有效，设置用户ID到Context中
    c.set('userId', userId);
    console.log('User authenticated:', userId);
    
    await next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return c.json(ResponseUtil.error("Token验证失败", 401), 401);
  }
};

/**
 * 获取当前用户ID
 * @param c Hono Context
 * @returns 用户ID，如果未认证则返回null
 */
export const getCurrentUserId = (c: Context<{ Bindings: Env }>): string | null => {
  return c.get('userId') || null;
};

/**
 * 检查用户是否已认证
 * @param c Hono Context
 * @returns 是否已认证
 */
export const isAuthenticated = (c: Context<{ Bindings: Env }>): boolean => {
  return !!c.get('userId');
}; 