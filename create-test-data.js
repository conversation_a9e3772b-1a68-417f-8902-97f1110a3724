const BASE_URL = 'http://localhost:8787';

async function createTestData() {
    console.log('🧪 开始创建测试数据...\n');

    try {
        // 1. 先创建分类
        console.log('📂 创建测试分类...');
        const categoryFormData = new FormData();
        categoryFormData.append('categoryName', '编程技术');
        
        const categoryResponse = await fetch(`${BASE_URL}/category`, {
            method: 'POST',
            body: categoryFormData
        });
        
        const categoryResult = await categoryResponse.json();
        console.log('分类创建结果:', categoryResult);
        
        if (categoryResult.code !== 200) {
            throw new Error('分类创建失败');
        }
        
        const categoryId = categoryResult.data[0].id;
        console.log('✅ 分类创建成功，ID:', categoryId);

        // 2. 创建第一本书
        console.log('\n📖 创建第一本测试书籍...');
        const formData1 = new FormData();
        formData1.append('title', 'JavaScript 高级教程');
        formData1.append('categoryId', categoryId);
        formData1.append('isFree', 'true');
        
        // 创建假的文件
        const pdfBlob1 = new Blob(['fake pdf content 1'], { type: 'application/pdf' });
        const coverBlob1 = new Blob(['fake image content 1'], { type: 'image/jpeg' });
        formData1.append('pdf', pdfBlob1, 'js-tutorial.pdf');
        formData1.append('cover', coverBlob1, 'js-cover.jpg');

        const bookResponse1 = await fetch(`${BASE_URL}/book`, {
            method: 'POST',
            body: formData1
        });

        const bookResult1 = await bookResponse1.json();
        console.log('第一本书创建结果:', bookResult1);
        
        if (bookResult1.code !== 200) {
            throw new Error('第一本书创建失败');
        }
        
        const book1Id = bookResult1.data[0].id;
        console.log('✅ 第一本书创建成功，ID:', book1Id);

        // 3. 创建第二本书
        console.log('\n📖 创建第二本测试书籍...');
        const formData2 = new FormData();
        formData2.append('title', 'React 实战指南');
        formData2.append('categoryId', categoryId);
        formData2.append('isFree', 'false');
        
        const pdfBlob2 = new Blob(['fake pdf content 2'], { type: 'application/pdf' });
        const coverBlob2 = new Blob(['fake image content 2'], { type: 'image/jpeg' });
        formData2.append('pdf', pdfBlob2, 'react-guide.pdf');
        formData2.append('cover', coverBlob2, 'react-cover.jpg');

        const bookResponse2 = await fetch(`${BASE_URL}/book`, {
            method: 'POST',
            body: formData2
        });

        const bookResult2 = await bookResponse2.json();
        console.log('第二本书创建结果:', bookResult2);
        
        if (bookResult2.code !== 200) {
            throw new Error('第二本书创建失败');
        }
        
        const book2Id = bookResult2.data[0].id;
        console.log('✅ 第二本书创建成功，ID:', book2Id);

        // 4. 验证书籍列表
        console.log('\n📋 验证书籍列表...');
        const listResponse = await fetch(`${BASE_URL}/book/list`);
        const listResult = await listResponse.json();
        console.log('书籍列表:', listResult);

        // 5. 测试 home banner 接口
        console.log('\n🏠 测试 Home Banner 接口...');
        const bannerResponse = await fetch(`${BASE_URL}/api/v1/home/<USER>/banner`);
        const bannerResult = await bannerResponse.json();
        console.log('Banner结果:', bannerResult);

        console.log('\n🎉 测试数据创建完成！');
        console.log('📝 建议：将以下ID更新到 wrangler.jsonc 的 GUEST_BANNER 中：');
        console.log(`"GUEST_BANNER": "[\\"${book1Id}\\", \\"${book2Id}\\"]"`);

    } catch (error) {
        console.error('❌ 创建测试数据失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
createTestData(); 